"use client";
import { Container } from "@mantine/core";
import React from "react";
import { useMediaQuery } from "@mantine/hooks";
import "devextreme/dist/css/dx.light.css";
import "./styles.css";
import Head from "next/head";
import RootLayout from "@/app/layout";

export default function PulicLayout({ children }) {
  const isMobile = useMediaQuery("(max-width: 768px)"); // Mantine uses CSS media query strings

  return (
    <RootLayout
      bodyStyle={{
        background:
          "url('/background.svg') center top / cover no-repeat, linear-gradient(180deg, rgba(0, 75, 139, 0.2) 0%, rgba(0, 75, 139, 0.8) 100%)",
        backgroundAttachment: "fixed",
      }}
    >
      <Head>
        <title>My page title</title>
      </Head>
      <Container size="md" px={isMobile ? 0 : 16}>
        {children}
      </Container>
    </RootLayout>
  );
}
