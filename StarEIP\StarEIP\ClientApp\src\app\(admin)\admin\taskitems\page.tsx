"use client";

import React, { useState, useEffect, useRef } from "react";
import DataGrid, {
  <PERSON>umn,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
} from "devextreme-react/data-grid";
import { createStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { Button } from "@mantine/core";
import { IconRefresh } from "@tabler/icons-react";
import Splitter, { Item as SplitterItem } from "devextreme-react/splitter";
import { FocusedRowChangedEvent } from "devextreme/ui/data_grid";
import TaskDetailsContent from "./components/TaskDetailsContent";
import { api } from "@/api/generated";
import { FullTaskDetailDtoSchema } from "@/api/types";

const serviceUrl = urlHelpers.getAbsoluteURL("api/taskitems");

const TaskItemsPage = () => {
  const [remoteDataSource, setRemoteDataSource] = useState<any>(null);
  const [selectedTask, setSelectedTask] =
    useState<FullTaskDetailDtoSchema | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const dataGridRef = useRef<any>(null);

  const loadTaskItemsDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        onBeforeSend: (_, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          console.error("Error loading TaskItems:", e);
        },
      }),
    );
  };

  const fetchTaskDetails = async (taskId: number) => {
    if (!taskId) return;

    setIsLoading(true);
    try {
      const response = await api.taskItems.getApiTaskItemsTaskId(taskId);
      setSelectedTask(response.data);
    } catch (error) {
      console.error("Error fetching task details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRowSelection = (e: FocusedRowChangedEvent<any, any>) => {
    if (e.row?.data?.id) {
      fetchTaskDetails(e.row.data.id);
    }
  };

  useEffect(() => {
    loadTaskItemsDataSource();
  }, []);

  return (
    <Splitter id="taskSplitter">
      <SplitterItem resizable={true} size="75%" minSize="70px">
        <div style={{ height: "100%", width: "100%" }}>
          <DataGrid
            ref={dataGridRef}
            dataSource={remoteDataSource}
            remoteOperations
            height="100%"
            width="100%"
            allowColumnReordering={true}
            allowColumnResizing={true}
            columnAutoWidth={true}
            showBorders={true}
            columnResizingMode={"widget"}
            showColumnLines={false}
            rowAlternationEnabled
            focusedRowEnabled
            autoNavigateToFocusedRow
            onFocusedRowChanged={handleRowSelection}
          >
            <Toolbar>
              <Item location="before">
                <Button
                  leftSection={<IconRefresh size="1rem" />}
                  onClick={loadTaskItemsDataSource}
                >
                  Refresh
                </Button>
              </Item>
              <Item name="searchPanel" locateInMenu="auto" location="before" />
              <Item name="exportButton" locateInMenu="auto" location="after" />
            </Toolbar>
            <RemoteOperations groupPaging={true} />
            <ColumnFixing enabled={true} />
            <SearchPanel visible width={250} />
            <HeaderFilter visible />
            <ColumnChooser enabled />
            <Sorting mode="multiple" />
            <Paging defaultPageSize={40} />
            <Pager showPageSizeSelector />
            <FilterPanel visible />
            <FilterBuilderPopup />
            <Export enabled={true} />

            <Column dataField="id" caption="ID" allowEditing={false} />
            <Column dataField="childId" caption="Child ID" visible={false} />
            <Column dataField="childName" caption="Child Name" />
            <Column dataField="assignedToUserId" caption="Assigned To ID" />
            <Column dataField="title" caption="Title" />
            <Column dataField="description" caption="Description" />
            <Column dataField="status" caption="Status" />
            <Column
              dataField="assignedToUser.firstName"
              caption="Assigned To"
              calculateCellValue={(rowData) =>
                rowData.assignedToUser
                  ? `${rowData.assignedToUser.firstName} ${rowData.assignedToUser.lastName}`
                  : ""
              }
            />
            <Column
              dataField="createdAt"
              caption="Created Date"
              dataType="date"
              format="shortDateShortTime"
            />
            <Column dataField="dueAt" caption="Due Date" dataType="date" />
          </DataGrid>
        </div>
      </SplitterItem>
      <SplitterItem resizable={true} minSize="250px" collapsible>
        <TaskDetailsContent
          fullTaskDetails={selectedTask}
          isLoading={isLoading}
          showEmailModal={true}
          onTaskUpdated={(updatedTask) => {
            if (updatedTask) {
              setSelectedTask(updatedTask);
              loadTaskItemsDataSource();
            }
          }}
        />
      </SplitterItem>
    </Splitter>
  );
};

export default TaskItemsPage;
