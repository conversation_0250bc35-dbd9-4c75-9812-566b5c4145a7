"use client";

import { Container, Loader } from "@mantine/core";
import PatientForm from "../../PatientForm";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import urlHelpers from "@/app/urlHelpers";
import axios from "axios";
import { ChildDetailsDtoSchema } from "@/api/types";

const serviceUrl = urlHelpers.getAbsoluteURL("api/children");
const physicianServiceUrl = urlHelpers.getAbsoluteURL("api/physicians");

export interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

const EditChildPage = ({ params }: PageProps) => {
  const router = useRouter();
  const [childData, setChildData] = useState<ChildDetailsDtoSchema | null>(
    null,
  );
  const [physicians, setPhysicians] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    params.then(({ id }) => {
      const fetchData = async () => {
        try {
          const [childResponse, physiciansResponse] = await Promise.all([
            axios.get(`${serviceUrl}/${id}`),
            axios.get(`${physicianServiceUrl}`),
          ]);
          setChildData(childResponse.data);
          setPhysicians(physiciansResponse.data.data);
        } catch (e) {
          console.error("Error fetching child data:", e);
          router.push("/admin/patients");
        } finally {
          setIsLoading(false);
        }
      };
      fetchData();
    });
  }, [router, params]);

  const handleCancel = () => {
    router.push("/admin/patients");
  };

  const handleSubmit = async (child: ChildDetailsDtoSchema) => {
    try {
      const url = urlHelpers.getAbsoluteURL(`api/children/${child.id}`);
      const response = await axios.put(url, child);
      if (response.status === 200) {
        router.push("/admin/patients");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  if (isLoading) {
    return (
      <Container p="lg" fluid>
        <Loader />
      </Container>
    );
  }

  if (!childData) {
    return (
      <Container p="lg" fluid>
        <p>No child found with the given ID.</p>
      </Container>
    );
  }

  return (
    <Container p="lg" fluid>
      <PatientForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        initialData={childData}
        isEditMode={true}
        physicians={physicians}
      />
    </Container>
  );
};

export default EditChildPage;
