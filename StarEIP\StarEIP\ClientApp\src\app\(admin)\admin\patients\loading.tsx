"use client";
import React, { useEffect } from "react";

const Loading: React.FC = () => {
  useEffect(() => {
    const timer = setTimeout(() => {
      window.location.reload();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      style={{
        position: "fixed",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
        height: "100%",
        backgroundColor: "aliceblue",
        zIndex: "500",
        color: "blue",
        opacity: "0.6",
      }}
    >
      <h1>Loading...</h1>
      <p>tasks page loading</p>
    </div>
  );
};

export default Loading;
