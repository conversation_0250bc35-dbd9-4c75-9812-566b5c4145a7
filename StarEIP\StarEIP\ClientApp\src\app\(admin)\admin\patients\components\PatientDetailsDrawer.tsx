"use client";

import React, { useState, useEffect } from "react";
import { Drawer, Text, Tabs, Loader } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import {
  IconBabyCarriage,
  IconClipboardList,
  IconClock,
  IconFileDescription,
  IconLink,
  IconFileImport,
} from "@tabler/icons-react";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { ChildDetailsDtoSchema } from "@/api/types";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";
import { NotesSection } from "@/app/(admin)/admin/notes/NotesSection";
import AuthorizationsTable from "@/app/(admin)/admin/authorizations/AuthorizationsTable";
import DocumentStorage from "@/app/(admin)/admin/patients/[id]/components/DocumentStorage";
import ImportChildInfoAuthorizationTable from "@/app/(admin)/admin/patients/components/ImportChildInfoAuthorizationTable";
import { ActionIcon, Tooltip } from "@mantine/core";

interface PatientDetailsDrawerProps {
  childId: number;
  onClose?: () => void;
  buttonText?: React.ReactNode;
  buttonSize?: "xs" | "sm" | "md" | "lg" | "xl";
  defaultTab?: string;
  useHyperlink?: boolean;
}

const PatientDetailsDrawer: React.FC<PatientDetailsDrawerProps> = ({
  childId,
  onClose,
  buttonText = "View Patient",
  buttonSize = "xs",
  defaultTab = "basic_info",
  useHyperlink = false,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [childDetails, setChildDetails] =
    useState<ChildDetailsDtoSchema | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string | null>(defaultTab);

  const handleClose = () => {
    close();
    if (onClose) {
      onClose();
    }
  };

  const fetchChildDetails = async () => {
    if (!childId) return;

    setIsLoading(true);
    try {
      const response = await axios.get(
        urlHelpers.getAbsoluteURL(`api/children/${childId}`),
      );
      setChildDetails(response.data);
    } catch (error) {
      console.error("Error fetching child details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (opened && childId) {
      fetchChildDetails();
    }
  }, [opened, childId]);

  // Check if buttonText is a React element (icon)
  const isIcon = React.isValidElement(buttonText);

  return (
    <>
      {useHyperlink ? (
        <Text
          onClick={open}
          c="blue"
          style={{ textDecoration: "underline", cursor: "pointer" }}
          size={buttonSize}
        >
          {buttonText}
        </Text>
      ) : isIcon ? (
        <ActionIcon
          onClick={open}
          size={buttonSize}
          color="blue"
          variant="subtle"
        >
          {buttonText}
        </ActionIcon>
      ) : (
        <ActionIcon
          onClick={open}
          size={buttonSize}
          color="blue"
          variant="subtle"
        >
          <IconBabyCarriage size="1rem" />
        </ActionIcon>
      )}

      <Drawer
        opened={opened}
        onClose={handleClose}
        position="right"
        size="50%"
        padding="xs"
        styles={{
          content: { display: "flex", flexDirection: "column" },
          body: { flex: 1, overflow: "auto" },
        }}
        overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
        closeOnClickOutside
        title={
          childDetails
            ? `Patient: ${childDetails.firstName} ${childDetails.lastName}`
            : `Patient Details`
        }
      >
        {isLoading ? (
          <Loader />
        ) : childDetails ? (
          <Tabs
            value={activeTab}
            onChange={setActiveTab}
            style={{
              flexGrow: 1,
              display: "flex",
              flexDirection: "column",
              height: "100%",
            }}
          >
            <Tabs.List>
              <Tabs.Tab
                value="basic_info"
                leftSection={<IconBabyCarriage size={16} />}
              >
                Basic Info
              </Tabs.Tab>
              <Tabs.Tab
                value="auth"
                leftSection={<IconClipboardList size={16} />}
              >
                Authorizations
              </Tabs.Tab>
              <Tabs.Tab
                value="docs"
                leftSection={<IconFileDescription size={16} />}
              >
                Documents
              </Tabs.Tab>
              <Tabs.Tab value="notes" leftSection={<IconClock size={16} />}>
                Notes
              </Tabs.Tab>
              <Tabs.Tab
                value="hub_auth"
                leftSection={<IconFileImport size={16} />}
              >
                HUB Authorizations
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="basic_info">
              <ChildInfo child={childDetails} />
            </Tabs.Panel>

            <Tabs.Panel
              value="auth"
              style={{ height: "100%", display: "flex", flex: 1, flexGrow: 1 }}
            >
              <AuthorizationsTable childId={childDetails.id!} />
            </Tabs.Panel>

            <Tabs.Panel
              value="docs"
              style={{ height: "100%", display: "flex", flex: 1, flexGrow: 1 }}
            >
              <DocumentStorage childId={childDetails.id!} />
            </Tabs.Panel>

            <Tabs.Panel value="notes">
              <NotesSection
                entityType="child"
                entityId={childDetails.id!}
                maxLength={500}
                enableDelete={true}
              />
            </Tabs.Panel>

            <Tabs.Panel
              value="hub_auth"
              style={{ height: "100%", display: "flex", flex: 1, flexGrow: 1 }}
            >
              <ImportChildInfoAuthorizationTable
                programId={childDetails.programId?.toString()}
              />
            </Tabs.Panel>
          </Tabs>
        ) : (
          <Text>No patient data found</Text>
        )}
      </Drawer>
    </>
  );
};

export default PatientDetailsDrawer;
