"use client";
import React, { useEffect } from "react";
import AdminAppShell from "@/app/(admin)/components/AppShell";
import useAuthStore from "@/app/store/AuthStore";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";

const serviceUrl = urlHelpers.getAbsoluteURL("/api/auth/profile");

const AdminLayout = ({ children }: { children: React.ReactNode }) => {
  const setPermissions = useAuthStore((state) => state.setPermissions);

  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        const response = await axios.get(serviceUrl);
        setPermissions(response.data.permissions);
        localStorage.setItem("jwtToken", response.data.token);
      } catch (error) {
        console.error("Error fetching permissions:", error);
      }
    };
    fetchPermissions();
  }, [setPermissions]);

  return <AdminAppShell>{children}</AdminAppShell>;
};

export default AdminLayout;
