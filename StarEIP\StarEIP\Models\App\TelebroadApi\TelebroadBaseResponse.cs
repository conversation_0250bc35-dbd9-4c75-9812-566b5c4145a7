﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace StarEIP.Models.App.TelebroadApi
{
    /// <summary>
    /// Base response class for Telebroad API responses
    /// </summary>
    /// <typeparam name="T">The type of the result</typeparam>
    public class TelebroadBaseResponse<T>
    {
        public TelebroadErrorObject error { get; set; }
        public T result { get; set; }
    }

    public class TelebroadErrorObject
    {
        public int code { get; set; }
        public string message { get; set; }

        public override string ToString()
        {
            return $"Code: {code}, Message: {message}";
        }
    }

    public class StringOrNumberConverter : JsonConverter<string>
    {
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.TokenType switch
            {
                JsonTokenType.String => reader.GetString() ?? string.Empty,
                JsonTokenType.Number => reader.GetInt64().ToString(),
                _ => throw new JsonException()
            };
        }

        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value);
        }
    }

    public class StringToLongConverter : JsonConverter<long>
    {
        public override long Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return long.TryParse(reader.GetString(), out var value) ? value : 0;
        }

        public override void Write(Utf8JsonWriter writer, long value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString());
        }
    }

    public class StringToIntConverter : JsonConverter<int>
    {
        public override int Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.TokenType switch
            {
                JsonTokenType.String => int.TryParse(reader.GetString(), out var value) ? value : throw new JsonException("Invalid string value for int conversion."),
                JsonTokenType.Number => reader.GetInt32(),
                _ => throw new JsonException("Invalid token type for int conversion.")
            };
        }

        public override void Write(Utf8JsonWriter writer, int value, JsonSerializerOptions options)
        {
            writer.WriteNumberValue(value);
        }
    }
}
