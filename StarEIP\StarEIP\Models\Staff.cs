﻿using StarEIP.Models.SCR;
using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models
{
    public class Staff
    {
        public int Id { get; set; }

        [Column(TypeName = "varchar(255)")]
        public required string Email { get; set; }
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Status { get; set; }

        public ICollection<ScrForm>? SCRForms { get; set; }
    }
}
