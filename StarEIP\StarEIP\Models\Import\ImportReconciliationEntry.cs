﻿using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models.Import
{
    public class ImportReconciliationEntry
    {
        public int Id { get; set; }

        public string ChildName { get; set; }
        public string EiNumber { get; set; }
        public string Provider { get; set; }
        public DateTime SessionDate { get; set; }
        public DateTime ClaimCreated { get; set; }
        public string Type { get; set; }
        public string SubType { get; set; }
        public decimal BillAmount { get; set; }
        public string RemittStatus { get; set; }
        public DateTime? RemittDate { get; set; }
        public decimal PaymentAmount { get; set; }
        public string FundingSource { get; set; }
        public decimal Discrepancy { get; set; }
        public string AdjustReasonCodes { get; set; }

        [Column(TypeName = "varchar(8000)")]
        public string AdjustReasonDescription { get; set; }
        public string Reconciled { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime LastImportedAt { get; set; }
    }
}
