html {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
/* Full-page loading container */
#loading {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: white;
  /* Or any background color */
  z-index: 9999;
}

/* Spinner with rotating border */
.spinner {
  position: relative;
  border: 16px solid #f3f3f3;
  /* Light grey border */
  border-top: 16px solid #3498db;
  /* Blue top border */
  border-radius: 50%;
  width: 150px;
  height: 150px;
  animation: spin 2s linear infinite;
  /* Controls the spinning */
}

/* Spinner animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Image inside spinner remains static */
.spinner img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* Center the image */
  z-index: 1;
  /* Ensure the image stays on top */
}
