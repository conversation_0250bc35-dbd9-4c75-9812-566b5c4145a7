using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;

namespace StarEIP.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.ViewAuditLogs))]
    public class AuditLogsController : ControllerBase
    {
        private readonly StarEipDbContext _context;
        private readonly ILogger<AuditLogsController> _logger;

        public AuditLogsController(StarEipDbContext context, ILogger<AuditLogsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/AuditLogs
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                // Left join with the Users table to get the username
                var auditLogsWithUsernames = from audit in _context.AuditLogs
                                           join user in _context.Users on audit.UserId equals user.Id into userGroup
                                           from user in userGroup.DefaultIfEmpty()
                                           select new
                                           {
                                               audit.Id,
                                               audit.UserId,
                                               audit.IpAddress,
                                               audit.TableName,
                                               audit.PrimaryKey,
                                               audit.ColumnName,
                                               audit.OldValue,
                                               audit.NewValue,
                                               audit.Timestamp,
                                               audit.JwtGuid,
                                               audit.UserAgent,
                                               audit.PrimaryKeyString,
                                               Username = user != null ? user.UserName : null,
                                               FullName = user != null ? $"{user.FirstName} {user.LastName}".Trim() : null
                                           };

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions,
                    key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = [nameof(AuditLog.Id)];
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(auditLogsWithUsernames, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving audit logs");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
