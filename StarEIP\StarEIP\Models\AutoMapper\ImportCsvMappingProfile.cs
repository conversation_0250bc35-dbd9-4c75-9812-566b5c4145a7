using AutoMapper;
using StarEIP.Models.Import;

namespace StarEIP.Models.AutoMapper
{
    public class ImportCsvMappingProfile : Profile
    {
        public ImportCsvMappingProfile()
        {
            CreateMap<ReconciliationEntryCsv, ImportReconciliationEntry>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.LastImportedAt, opt => opt.Ignore());

            CreateMap<ChildInfoAuthorizationCsv, ImportChildInfoAuthorization>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.LastImportedAt, opt => opt.Ignore())
                .ForMember(dest => dest.EiChildId, opt => opt.MapFrom(src => src.EIChildID));

            CreateMap<ChildDetailReportCsv, ImportChildDetailReport>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.LastImportedAt, opt => opt.Ignore());

            CreateMap<PsAllChildrenCsv, ImportPsAllChildren>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.LastImportedAt, opt => opt.Ignore());

            CreateMap<ChildLookupReportCsv, ImportChildLookupReport>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.LastImportedAt, opt => opt.Ignore());
        }
    }
}
