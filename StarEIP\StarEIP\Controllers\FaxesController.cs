﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Flurl;
using Flurl.Http;
using StarEIP.Models;
using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.EntityFrameworkCore;
using Azure.Storage.Blobs;
using StarEIP.Models.Auth;

namespace StarEIP.Controllers
{
    [Route("api/faxes")]
    [ApiController]
    [Authorize(nameof(UserPermission.ViewFaxes))]
    public class FaxesController : ControllerBase
    {
        private readonly StarEipDbContext dbContext;
        private readonly ILogger<FaxesController> logger;
        private readonly BlobServiceClient blobServiceClient;

        public FaxesController(StarEipDbContext dbContext, ILogger<FaxesController> logger, BlobServiceClient blobServiceClient)
        {
            this.dbContext = dbContext;
            this.logger = logger;
            this.blobServiceClient = blobServiceClient;
        }

        [HttpGet]
        public async Task<IActionResult> GetFaxesAsync()
        {
            try
            {
                var faxes = dbContext.Faxes
                    .Include(f => f.Physician)
                    .Select(f => new
                    {
                        f.Id,
                        f.FaxId,
                        f.Direction,
                        f.FromNumber,
                        f.To,
                        f.Pages,
                        f.CreatedAt,
                        f.DownloadUrl,
                        f.PhysicianId,
                        PhysicianName = f.Physician.Name
                    });

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                dbContext.ChangeTracker.LazyLoadingEnabled = true;
                dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = new[] { nameof(Fax.Id) };
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(faxes, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get faxes");
                throw;
            }
        }

        [HttpGet("{faxId}/pdf")]
        public async Task<IActionResult> GetFaxPdfAsync(string faxId)
        {
            try
            {
                var containerClient = blobServiceClient.GetBlobContainerClient("faxes");
                var blobClient = containerClient.GetBlobClient($"{faxId}.pdf");

                if (await blobClient.ExistsAsync())
                {
                    var response = await blobClient.DownloadContentAsync();
                    var fileContent = response.Value.Content.ToArray();
                    return File(fileContent, "application/pdf", $"{faxId}.pdf");
                }
                else
                {
                    return NotFound();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get fax PDF");
                return StatusCode(500, "Internal server error");
            }
        }
        
        [HttpPost]
        public async Task<IActionResult> LinkChildToFax([FromBody] FaxChild faxChild)
        {
            dbContext.FaxChildren.Add(faxChild);
            await dbContext.SaveChangesAsync();

            return Ok();
        }
        
        public record LinkPhysicianRequest(int FaxId, int PhysicianId);
        [HttpPost("LinkPhysician")]
        public async Task<IActionResult> LinkPhysicianToFax([FromBody] LinkPhysicianRequest request)
        {
            var fax = await dbContext.Faxes.FindAsync(request.FaxId);
            if (fax == null)
            {
                return NotFound();
            }
            fax.PhysicianId = request.PhysicianId;
            await dbContext.SaveChangesAsync();
            return Ok();
        }

    }
}
