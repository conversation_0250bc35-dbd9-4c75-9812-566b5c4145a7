using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.DTOs;
using StarEIP.Models;
using StarEIP.Models.App;
using StarEIP.Models.Tasks;
using StarEIP.Services.Core;
using StarEIP.Services.Data;
using TaskStatus = StarEIP.Models.Tasks.TaskStatus;

namespace StarEIP.Controllers
{
    [Route("api/TaskItems")]
    [ApiController]
    public class TaskItemsController : Controller
    {
        private readonly StarEipDbContext _dbContext;
        private readonly EmailService emailService;
        private readonly ChildDataService _childDataService;

        public TaskItemsController(StarEipDbContext dbContext, EmailService emailService, ChildDataService childDataService)
        {
            _dbContext = dbContext;
            this.emailService = emailService;
            _childDataService = childDataService;
        }

        [HttpGet]
        public async Task<IActionResult> GetTaskItems()
        {
            try
            {
                var taskItems = GetTaskDetailsDtos();
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                _dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                if (Request.Query.ContainsKey("assignedToUserId") && Request.Query["assignedToUserId"] == "current")
                {
                    int currentUserId = User.GetUserId();
                    taskItems = taskItems.Where(t => t.AssignedToUserId == currentUserId);
                }

                loadOptions.PrimaryKey = new[] { nameof(TaskDetailsDto.Id) };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(taskItems, loadOptions));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetTaskItems: {ex.Message}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statuses")]
        public async Task<ActionResult<List<TaskStatus>>> GetTaskStatuses()
        {
            try
            {
                var statuses = await _dbContext.TaskStatuses.ToListAsync();
                return Ok(statuses);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetTaskStatuses: {ex.Message}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        public async Task<ActionResult<FullTaskDetailDto>> CreateTask([FromBody] CreateTaskRequest request)
        {
            try
            {
                var template = request.TaskTemplateId != null
                    ? await _dbContext.TaskTemplates.FindAsync(request.TaskTemplateId)
                    : await _dbContext.TaskTemplates.FirstOrDefaultAsync();

                if (template == null)
                    return BadRequest("No valid TaskTemplate found");

                var status = request.TaskStatusId?.Value != null
                    ? await _dbContext.TaskStatuses.FindAsync(request.TaskStatusId.Value)
                    : await _dbContext.TaskStatuses.FirstOrDefaultAsync();

                if (status == null)
                    return BadRequest("No valid TaskStatus found");

                var task = new TaskItem
                {
                    Title = !string.IsNullOrWhiteSpace(request.Title)
                        ? request.Title
                        : $"Task - {DateTime.Now:MM/dd/yyyy}",
                    TaskTemplateId = template.Id,
                    TaskStatusId = status.Id,
                    ChildId = request.ChildId,
                    CreatedAt = request.CreatedAt ?? DateTime.UtcNow,
                    DueAt = request.DueAt?.Value ?? DateTime.UtcNow.AddDays(7)
                };

                _dbContext.TaskItems.Add(task);
                await _dbContext.SaveChangesAsync();

                await ApplyTaskUpdates(task, request);
                await _dbContext.SaveChangesAsync();

                var createdTask = await GetTaskDetailsDtos().SingleAsync(t => t.Id == task.Id);
                FullTaskDetailDto fullTaskDetail = await GetFullTaskDetailDto(createdTask);
                return Ok(fullTaskDetail);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CreateTask: {ex.Message}");
                return StatusCode(500, "Internal server error");
            }
        }


        [HttpPut("{taskId}")]
        public async Task<ActionResult<FullTaskDetailDto>> UpdateSingleTask(int taskId, [FromBody] SingleUpdateTaskRequest request)
        {
            try
            {
                var task = await _dbContext.TaskItems.FindAsync(taskId);
                if (task == null)
                    return NotFound($"Task with ID {taskId} not found");

                await ApplyTaskUpdates(task, request);
                await _dbContext.SaveChangesAsync();

                var updatedTask = await GetTaskDetailsDtos().SingleAsync(t => t.Id == task.Id);
                FullTaskDetailDto fullTaskDetail = await GetFullTaskDetailDto(updatedTask);
                return Ok(fullTaskDetail);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in UpdateSingleTask: {ex.Message}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("bulk-update")]
        public async Task<ActionResult<List<TaskDetailsDto>>> BulkUpdateTasks([FromBody] BulkUpdateTasksRequest request)
        {
            try
            {
                var tasks = await _dbContext.TaskItems.Where(t => request.TaskIds.Contains(t.Id)).ToListAsync();

                if (!tasks.Any())
                    return NotFound("No matching tasks found");

                foreach (var task in tasks)
                {
                    await ApplyTaskUpdates(task, request);
                }
                await _dbContext.SaveChangesAsync();

                var updatedTasks = await GetTaskDetailsDtos().Where(t => request.TaskIds.Contains(t.Id)).ToListAsync();
                return Ok(updatedTasks);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in BulkUpdateTasks: {ex.Message}");
                return StatusCode(500, "Internal server error");
            }
        }

        public record FullTaskDetailDto(TaskDetailsDto TaskDetailsDto, ChildDetailsDto? ChildDetailsDto, List<TaskItemLink> TaskItemLinks, List<TaskItemEmail> emails);

        [HttpGet("{taskId}")]
        public async Task<ActionResult<FullTaskDetailDto>> GetTaskItemById(int taskId)
        {
            try
            {
                var taskItem = await GetTaskDetailsDtos().Where(t => t.Id == taskId).FirstOrDefaultAsync();
                FullTaskDetailDto fullTaskDetail = await GetFullTaskDetailDto(taskItem);
                return Ok(fullTaskDetail);
            }

            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetTaskItemById: {ex.Message}");
                return StatusCode(500, "Internal server error");
            }
        }

        private async Task<FullTaskDetailDto> GetFullTaskDetailDto(TaskDetailsDto taskItem)
        {
            ChildDetailsDto? child = null;
            if (taskItem?.ChildId.HasValue == true)
            {
                child = await _childDataService.GetChildrenDto().SingleAsync(c => c.Id == taskItem.ChildId);
            }
            var links = await _dbContext.TaskItemLinks.Where(l => l.TaskItemId == taskItem.Id).ToListAsync();
            var emails = await _dbContext.TaskItemEmails
                .Include(e => e.EmailMessage)
                .Where(e => e.TaskItemId == taskItem.Id)
                .ToListAsync();
            var fullTaskDetail = new FullTaskDetailDto(taskItem, child, links, emails);
            return fullTaskDetail;
        }

        private IQueryable<TaskDetailsDto> GetTaskDetailsDtos()
        {
            return _dbContext.TaskItems
                .Include(t => t.TaskTemplate)
                .Include(t => t.Status)
                .Include(t => t.AssignedToUser)
                .Include(t => t.Child)
                .Select(task => new TaskDetailsDto
                {
                    Id = task.Id,
                    Title = task.Title,
                    Description = task.TaskTemplate.Description,
                    Status = task.Status.Name,
                    CreatedAt = task.CreatedAt,
                    DueAt = task.DueAt,
                    AssignedToUserId = task.AssignedToUserId,
                    AssignedToUser = task.AssignedToUser != null ? new UserDto
                    {
                        Id = task.AssignedToUser.Id,
                        FirstName = task.AssignedToUser.FirstName ?? "",
                        LastName = task.AssignedToUser.LastName ?? "",
                        Email = task.AssignedToUser.Email ?? "",
                        UserName = task.AssignedToUser.UserName ?? ""
                    } : null,
                    ChildId = task.ChildId,
                    ChildName = task.Child != null ? task.Child.PatientFullName ?? $"{task.Child.FirstName} {task.Child.LastName}".Trim() : null
                }).AsQueryable();
        }

        private async Task ApplyTaskUpdates(TaskItem task, ITaskUpdatePayload request)
        {
            if (request.AssignedToUserId?.Update == true)
                task.AssignedToUserId = request.AssignedToUserId.Value;

            if (request.TaskStatusId?.Update == true)
                task.TaskStatusId = request.TaskStatusId.Value.Value;

            if (request.DueAt?.Update == true)
                task.DueAt = request.DueAt.Value;

            if (request.UnlinkAllLinks == true)
            {
                var links = await _dbContext.TaskItemLinks
                    .Where(l => l.TaskItemId == task.Id)
                    .ToListAsync();
                _dbContext.TaskItemLinks.RemoveRange(links);
            }

            if (request.LinkedItems?.Any() == true)
            {
                foreach (var item in request.LinkedItems)
                {
                    var exists = await _dbContext.TaskItemLinks.AnyAsync(l => l.TaskItemId == task.Id && l.LinkTable == item.TableName && l.LinkId == item.ItemId);

                    if (!exists)
                    {
                        _dbContext.TaskItemLinks.Add(new TaskItemLink
                        {
                            TaskItemId = task.Id,
                            LinkTable = item.TableName,
                            LinkId = item.ItemId,
                            LinkType = item.LinkType ?? "Generic",
                            CreatedAt = DateTime.UtcNow
                        });
                    }
                }
            }
        }

        // Shared Interfaces and DTOs

        public class CreateTaskRequest : ITaskUpdatePayload
        {
            public string? Title { get; set; }
            public int? TaskTemplateId { get; set; } // optional; use default if null
            public int? ChildId { get; set; }
            public DateTime? CreatedAt { get; set; } = DateTime.UtcNow;

            public FieldUpdate<int?>? AssignedToUserId { get; set; }
            public FieldUpdate<int?>? TaskStatusId { get; set; }
            public FieldUpdate<DateTime?>? DueAt { get; set; }

            public bool? UnlinkAllLinks => false;
            public List<TaskLinkItem>? LinkedItems { get; set; }
        }


        public interface ITaskUpdatePayload
        {
            FieldUpdate<int?>? AssignedToUserId { get; }
            FieldUpdate<int?>? TaskStatusId { get; }
            FieldUpdate<DateTime?>? DueAt { get; }
            bool? UnlinkAllLinks { get; }
            List<TaskLinkItem>? LinkedItems { get; }
        }

        public class TaskLinkItem
        {
            public string TableName { get; set; } = string.Empty;
            public int ItemId { get; set; }
            public string? LinkType { get; set; }
        }

        public class FieldUpdate<T>
        {
            public bool Update { get; set; }
            public T? Value { get; set; }
        }

        public class SingleUpdateTaskRequest : ITaskUpdatePayload
        {
            public FieldUpdate<int?>? AssignedToUserId { get; set; }
            public FieldUpdate<int?>? TaskStatusId { get; set; }
            public FieldUpdate<DateTime?>? DueAt { get; set; }
            public bool? UnlinkAllLinks { get; set; }
            public List<TaskLinkItem>? LinkedItems { get; set; }
        }

        public class BulkUpdateTasksRequest : ITaskUpdatePayload
        {
            public List<int> TaskIds { get; set; } = new();
            public FieldUpdate<int?>? AssignedToUserId { get; set; }
            public FieldUpdate<int?>? TaskStatusId { get; set; }
            public FieldUpdate<DateTime?>? DueAt { get; set; }
            public bool? UnlinkAllLinks { get; set; }
            public List<TaskLinkItem>? LinkedItems { get; set; }
        }
    }
}
