﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddChildDetailReport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Import_ChildDetailReport",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EiChildId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Dob = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Address = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    City = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    State = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Zip = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Gender = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Ethnicity = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Race = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ChildAge = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    PrimaryLanguage = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CountyOfResidence = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    AddressCounty = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferralDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    AtRiskReferralDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ReferralAtRiskStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferralType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferralMethod = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferralReason = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ReferralSourceType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EligibilityStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    GuardianLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    GuardianFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    GuardianPhone = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CoordinatorLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CoordinatorFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CoordinatorPhone = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CoordinatorCompany = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodLastName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    EiodFirstName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CurrentIfspStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CurrentIfspEndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CurrentIfspStatus = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CurrentIfspType = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LastImportedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Import_ChildDetailReport", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Import_ChildDetailReport_EiChildId",
                table: "Import_ChildDetailReport",
                column: "EiChildId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Import_ChildDetailReport");
        }
    }
}
