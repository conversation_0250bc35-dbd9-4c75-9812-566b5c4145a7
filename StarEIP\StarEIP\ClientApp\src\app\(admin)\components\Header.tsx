"use client";

import { useMainStore } from "@/app/store/MainStore";
import { Group, Burger, Text } from "@mantine/core";

const Header = () => {
  const collapsed = useMainStore((state) => state.sideMenuCollapsed);
  const setCollapsed = useMainStore((state) => state.setSideMenuCollapsed);

  return (
    <Group h="100%" px="md" justify="space-between">
      <Group>
        <Burger
          opened={!collapsed}
          onClick={() => setCollapsed(!collapsed)}
          hiddenFrom="sm"
          size="sm"
        />
        <Text>Star EIP</Text>
      </Group>
      {/* Add any other header content here */}
    </Group>
  );
};

export default Header;
