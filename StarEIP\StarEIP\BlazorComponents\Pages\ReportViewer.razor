@page "/blazor/reportviewer"
@page "/blazor/reportviewer/{ReportName}"
@layout Shared.NoLayout
@rendermode InteractiveServer
@using Azure.Storage.Blobs
@using DevExpress.Blazor.Reporting
@using DevExpress.Blazor.Reporting.Models
@using DevExpress.XtraReports.UI
@inject DevExpress.XtraReports.Web.Extensions.ReportStorageWebExtension ReportStorage
@inject ILogger<ReportDesigner> <PERSON><PERSON>
@inject NavigationManager NavigationManager
@using DevExpress.Blazor
@using StarEIP.Models
@inject BlobServiceClient BlobServiceClient
@inject StarEipDbContext _dbContext

<link href="_content/DevExpress.Blazor.Themes/blazing-berry.bs5.css" rel="stylesheet" />
<link href="_content/DevExpress.Blazor.Reporting.Viewer/css/dx-blazor-reporting-components.bs5.css" rel="stylesheet" />

@if (!string.IsNullOrEmpty(Message))
{
    <div class="alert @(Message.StartsWith("Error") ? "alert-danger" : "alert-success") alert-dismissible fade show" role="alert">
        @Message
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close" @onclick="() => Message = null"></button>
    </div>
}

@if (Report != null)
{
    <DxReportViewer @ref="reportViewer" 
                    Report="@Report"
                    OnCustomizeToolbar="OnCustomizeToolbar"
                    RootCssClasses="w-100 h-100" />
}

@code {
    [Parameter]
    public string? ReportName { get; set; }

    DxReportViewer? reportViewer;
    XtraReport? Report;
    string? Message;

    void OnCustomizeToolbar(ToolbarModel toolbarModel)
    {
        toolbarModel.AllItems.Add(new ToolbarItem()
            {
                // Use Open Iconic's icon.
                IconCssClass = "oi oi-data-transfer-download",
                Text = "Save",
                AdaptiveText = "Save report",
                AdaptivePriority = 1,
                Click = async (args) =>
                {
                    try
                    {
                        if (Report == null)
                        {
                            Logger.LogError("Report is null. Cannot save report.");
                            Message = "Error: Report is null. Cannot save report.";
                            return;
                        }
                        var childIdParameter = Report.Parameters["ChildId"];
                        var childIdString = childIdParameter?.Value?.ToString();
                        if (childIdString == null)
                        {
                            // Check query parameters for ChildId
                            var uri = new Uri(NavigationManager.Uri);
                            var queryParameters = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);
                            if (queryParameters.ContainsKey("ChildId"))
                            {
                                childIdString = queryParameters["ChildId"]!;
                            }
                            else
                            {
                                Logger.LogError("Report doesn't have a child id parameter nor child id in query params. Cannot save report.");
                                Message = "Error: Report doesn't have a child id parameter. Cannot save report.";
                                return;
                            }
                        }
                        if (!int.TryParse(childIdString.ToString(), out int childId))
                        {
                            Logger.LogError("Child id parameter is not an integer. Cannot save report.");
                            Message = "Error: Child id parameter is not an integer. Cannot save report.";
                            return;
                        }
                        using (var stream = new MemoryStream())
                        {
                            // Export the report to PDF
                            await Report.ExportToPdfAsync(stream);
                            stream.Position = 0;

                            var fileName = ReportName + ".pdf";

                            // Upload to blob storage
                            await UploadToBlobStorage(stream, fileName, childId);
                        }
                        Message = "Saved in the document storage";
                    }
                    catch (Exception ex)
                    {
                        Message = $"Error: An error occurred while saving the report: {ex.Message}";
                        Logger.LogError(ex, $"Error saving report: {ReportName}");
                    }
                }
            });
    }

    protected override void OnInitialized()
    {
        NavigationManager.LocationChanged += OnLocationChanged;
        LoadReport();
    }

    protected override void OnParametersSet()
    {
        LoadReport();
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        LoadReport();
        StateHasChanged();
    }

    private void LoadReport()
    {
        Message = null;
        Report = null;

        if (string.IsNullOrEmpty(ReportName))
        {
            Message = "No report name specified.";
            Logger.LogError(Message);
            return;
        }

        try
        {
            Logger.LogInformation($"Loading report: {ReportName}");
            var reportData = ReportStorage.GetData(ReportName);

            if (reportData != null && reportData.Length > 0)
            {
                using var stream = new MemoryStream(reportData);
                Report = XtraReport.FromXmlStream(stream);
            }
            else
            {
                Message = $"Failed to load report: {ReportName}. The report data is empty or null.";
                Logger.LogError(Message);
            }

            // Get the query parameters
            var uri = new Uri(NavigationManager.Uri);
            var queryParameters = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

            // Set the report parameters
            if (Report != null && queryParameters != null)
            {
                foreach (var param in queryParameters)
                {
                    if(string.IsNullOrEmpty(param.Key) || param.Key == "undefined")
                    {
                        continue;
                    }
                    var reportParam = Report.Parameters[param.Key];
                    if (reportParam != null)
                    {
                        reportParam.Value = Convert.ChangeType(param.Value.FirstOrDefault(), Report.Parameters[param.Key].Type);
                        reportParam.Visible = false;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Message = $"An error occurred while loading the report: {ex.Message}";
            Logger.LogError(ex, $"Error loading report: {ReportName}");
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }

    private async Task UploadToBlobStorage(Stream stream, string fileName, int childId)
    {
        var containerName = "child-attachments";
        var filePath = $"{childId}/{fileName}";

        var containerClient = BlobServiceClient.GetBlobContainerClient(containerName);
        await containerClient.CreateIfNotExistsAsync();

        var blobClient = containerClient.GetBlobClient(filePath);

        await blobClient.UploadAsync(stream, overwrite: true);

        // Create a ChildAttachment record
        var attachment = new ChildAttachment
        {
            ChildId = childId,
            FileName = fileName,
            FilePath = blobClient.Uri.ToString(),
            FileType = "application/pdf"
        };

        _dbContext.ChildAttachments.Add(attachment);
        await _dbContext.SaveChangesAsync();
    }
}