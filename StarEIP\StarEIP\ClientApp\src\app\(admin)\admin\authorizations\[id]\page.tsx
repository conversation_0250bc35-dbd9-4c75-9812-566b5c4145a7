"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import urlHelpers from "@/app/urlHelpers";
import axios from "axios";
import { Container, Loader, Tabs } from "@mantine/core";
import { AuthorizationWithChild } from "../../../../../../types/Authorization";
import AuthorizationInfo from "@/app/(admin)/admin/authorizations/AuthorizationInfo";
import ChildInfo from "@/app/(admin)/admin/patients/ChildInfo";
import DocumentStorage from "@/app/(admin)/admin/patients/[id]/components/DocumentStorage";

const serviceUrl = urlHelpers.getAbsoluteURL("api/authorizations");

const AuthorizationDetailPage = () => {
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;
  const [isLoading, setIsLoading] = useState(false);
  const [authorization, setAuthorization] =
    useState<AuthorizationWithChild | null>(null);

  useEffect(() => {
    const fetchAuthorization = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get(`${serviceUrl}/${id}`);
        setAuthorization(response.data);
      } catch (e) {
        console.error("Error fetching authorization data:", e);
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchAuthorization();
    }
  }, [id]);

  if (isLoading) {
    return (
      <Container p="lg" fluid>
        <Loader />
      </Container>
    );
  }

  if (!authorization) {
    return (
      <Container p="lg" fluid>
        <p>No authorization found with the given ID.</p>
      </Container>
    );
  }

  return (
    <Tabs
      defaultValue="auth_info"
      style={{
        flexGrow: 1,
        display: "flex",
        flexDirection: "column",
        height: "100%",
      }}
    >
      <Tabs.List>
        <Tabs.Tab value="auth_info">Authorization Info</Tabs.Tab>
        <Tabs.Tab value="child_info">Child Info</Tabs.Tab>
        <Tabs.Tab value="child_docs">Child Document Storage</Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="auth_info">
        <AuthorizationInfo authorization={authorization} />
      </Tabs.Panel>
      <Tabs.Panel value="child_info">
        <ChildInfo
          child={authorization.child ?? null}
          showAuthorizationsTab={false}
        />
      </Tabs.Panel>
      <Tabs.Panel
        value="child_docs"
        style={{ height: "100%", display: "flex", flex: 1, flexGrow: 1 }}
      >
        <DocumentStorage childId={authorization.childId} authId={Number(id)} />
      </Tabs.Panel>
    </Tabs>
  );
};

export default AuthorizationDetailPage;
