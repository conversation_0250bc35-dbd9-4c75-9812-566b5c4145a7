using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace StarEIP.Models.App
{
    public class AuditEntry
    {
        public AuditEntry(EntityEntry entry)
        {
            Entry = entry;
            Changes = [];
            TemporaryProperties = [];
        }

        public EntityEntry Entry { get; }
        public string TableName { get; set; }
        public int? UserId { get; set; }
        public string? IpAddress { get; set; }
        public int? PrimaryKey => int.TryParse(Entry.Properties.First(p => p.Metadata.IsPrimaryKey()).CurrentValue?.ToString(), out int pk) ? pk : null;
        public string? PrimaryKeyString => Entry.Properties.First(p => p.Metadata.IsPrimaryKey()).CurrentValue?.ToString();
        public List<AuditChange> Changes { get; }
        public List<PropertyEntry> TemporaryProperties { get; }
        public Guid? JwtGuid { get; set; }
        public string? UserAgent { get; set; }
    }

    public class AuditChange
    {
        public required string ColumnName { get; set; }
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
    }
}
