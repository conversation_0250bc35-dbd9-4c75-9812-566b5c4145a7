using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StarEIP.Services.Auth;

namespace StarEIP.Controllers;

[Authorize]
[Route("api/roles")]
[ApiController]
public class RolesController : ControllerBase
{
    private readonly IRoleService _roleService;

    public RolesController(IRoleService roleService)
    {
        _roleService = roleService;
    }
    
    [HttpGet]
    public async Task<IActionResult> GetAllRoles()
    {
        var roles = await _roleService.GetAllRolesAsync();
        return Ok(roles);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetRoleById(int id)
    {
        var role = await _roleService.GetRoleByIdAsync(id);
        if (role == null)
        {
            return NotFound();
        }

        return Ok(role);
    }
    
    public record CreateRoleRequest(string Name, string? Description);
    [HttpPost("create")]
    public async Task<IActionResult> CreateRole(CreateRoleRequest request)
    {
        var result = await _roleService.CreateRoleAsync(request.Name, request.Description);
        if (!result.Succeeded)
        {
            return BadRequest(result.Errors);
        }
        return Ok("Role created successfully");
    }

    [HttpDelete("{roleId}")]
    public async Task<IActionResult> DeleteRole(int roleId)
    {
        var role = await _roleService.GetRoleByIdAsync(roleId);
        if (role == null)
        {
            return NotFound();
        }

        var result = await _roleService.DeleteRoleAsync(role);
        if (!result.Succeeded)
        {
            return BadRequest(result.Errors);
        }
        return Ok("Role deleted successfully");
    }

}