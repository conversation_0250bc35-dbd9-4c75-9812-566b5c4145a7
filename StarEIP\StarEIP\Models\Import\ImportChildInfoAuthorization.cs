namespace StarEIP.Models.Import;

public class ImportChildInfoAuthorization
{
    public int Id { get; set; }

    public string BillingProvider { get; set; }
    public string EipProviderId { get; set; }
    public string EiChildId { get; set; }
    public string ChildFirstName { get; set; }
    public string ChildMiddleName { get; set; }
    public string ChildLastName { get; set; }
    public DateTime? Dob { get; set; }
    public string ChildAddress { get; set; }
    public string ChildCity { get; set; }
    public string ChildState { get; set; }
    public string ChildZip { get; set; }
    public string Sex { get; set; }
    public string CountyOfResidence { get; set; }
    public string ChildStatus { get; set; }
    public DateTime? DateAgingOut { get; set; }
    public string ServiceCoordinatorFirstName { get; set; }
    public string ServiceCoordinatorLastName { get; set; }
    public string ServiceCoordinatorCompany { get; set; }
    public string ServiceCoordinatorPhone { get; set; }
    public string ServiceCoordinatorEmail { get; set; }
    public string EiodFirstName { get; set; }
    public string EiodLastName { get; set; }
    public string EiodPhone { get; set; }
    public string EiodEmail { get; set; }
    public string Program { get; set; }
    public string EnrollmentType { get; set; }
    public string EnrollmentStatus { get; set; }
    public string AuthorizationNumber { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string LocationType { get; set; }
    public DateTime? SuspendedStartDate { get; set; }
    public DateTime? SuspendedEndDate { get; set; }
    public string Length { get; set; }
    public string Frequency { get; set; }
    public string FrequencyUnit { get; set; }
    public string VisitsPerDay { get; set; }
    public string IfspId { get; set; }
    public DateTime? IfspSignedDate { get; set; }
    public string IfspType { get; set; }
    public string IfspStatus { get; set; }
    public DateTime? IfspStartDate { get; set; }
    public DateTime? IfspEndDate { get; set; }
    public DateTime? ExitDate { get; set; }
    public string TherapistFirstName { get; set; }
    public string TherapistLastName { get; set; }
    public string TherapistNpi { get; set; }
    public string ReferringProviderFirstName { get; set; }
    public string ReferringProviderLastName { get; set; }
    public string ReferringProviderNpi { get; set; }
    public DateTime? LastSessionDate { get; set; }
    public int? TotalSessionsUsed { get; set; }
    public int? TotalSessionsAuthorized { get; set; }
    public int? NumberOfMakeupUnitsAuthorized { get; set; }
    public int? TotalMakeupUnitsUsed { get; set; }
    public int? MakeupUnitsRemaining { get; set; }
    public int? TotalCoVisitUnits { get; set; }
    public int? CoVisitUnitsUsed { get; set; }
    public int? CoVisitUnitsRemaining { get; set; }
    public int? RemainingUnitsSessions { get; set; }
    public DateTime CreatedAt { get; internal set; }
    public DateTime UpdatedAt { get; internal set; }
    public DateTime LastImportedAt { get; internal set; }
}