"use client";
import React from "react";
import ReportsTable from "./ReportsTable";
import { Title, Container, Button, Group } from "@mantine/core";
import Link from "next/link";
import { useHasPermission } from "@/app/store/AuthStore";

const ReportsPage: React.FC = () => {
  const hasDesignerPermission = useHasPermission("AllowReportDesigner");

  return (
    <Container fluid p="md">
      <Group justify="space-between" mb="md">
        <Title order={1}>Reports</Title>
        {hasDesignerPermission && (
          <Link href="/admin/reports/designer" passHref>
            <Button component="a">Open Designer</Button>
          </Link>
        )}
      </Group>
      <ReportsTable />
    </Container>
  );
};

export default ReportsPage;
