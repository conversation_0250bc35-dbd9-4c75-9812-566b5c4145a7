﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddScrFormInfoView : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
            CREATE VIEW [dbo].[ScrFormInfo] AS
            SELECT 
                sf.Id AS ScrFormId,
                sf.SubmissionDate,
                sf.ExpiryDate,
                sf.StaffId,
                s.FirstName + ' ' + s.LastName AS StaffName
            FROM 
                ScrForms sf
            JOIN 
                Staff s ON sf.StaffId = s.Id;
        ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP VIEW [dbo].[ScrFormInfo];");
        }
    }
}
