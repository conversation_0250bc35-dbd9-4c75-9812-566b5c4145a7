"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import useAuthStore, { UserPermission } from "@/app/store/AuthStore";
import { useForm } from "@mantine/form";
import {
  Alert,
  Box,
  Button,
  Container,
  Paper,
  PasswordInput,
  Text,
  TextInput,
  Title,
} from "@mantine/core";
import {
  IconAlertCircle,
  IconAt,
  IconLock,
  IconLogin,
} from "@tabler/icons-react";
import { api } from "@/api/generated";
import { LoginRequestSchema } from "@/api/types";

const serviceUrl = urlHelpers.getAbsoluteURL("api/auth");

const LoginPage = () => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const router = useRouter();

  const setPermission = useAuthStore((state) => state.setPermissions);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
    validate: {
      email: (value) =>
        /^\S+@\S+$/.test(value) ? null : "Invalid email address",
      password: (value) =>
        value.length >= 2
          ? null
          : "Password must be at least 8 characters long",
    },
  });

  const handleLogin = async (values: { email: string; password: string }) => {
    setErrorMessage("");
    setLoading(true);
    try {
      const payload: LoginRequestSchema = {
        emailAddress: values.email,
        password: values.password,
      };
      //const response = await axios.post(`${serviceUrl}/login`, payload);
      const response = await api.auth.postApiAuthLogin(payload);
      if (response.status == 200) {
        localStorage.setItem("jwtToken", response.data.token || "");
        setPermission(response.data.permissions as UserPermission[]);
        router.replace("/admin/dashboard");
      } else {
        setErrorMessage("Invalid credentials");
      }
    } catch (error) {
      console.error(error);
      setErrorMessage("Invalid email or password, Please try again");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container size={700} my={40}>
      <Title ta="center" fw={900} size="h2" mb={20}>
        Welcome back!
      </Title>
      <Text c="dimmed" size="md" ta="center" mb={30}>
        Enter your email and password to log in.
      </Text>

      <Paper withBorder shadow="md" p={40} radius="md">
        {errorMessage && (
          <Alert
            icon={<IconAlertCircle size="1.1rem" />}
            title="Error"
            color="red"
            mb="xl"
          >
            {errorMessage}
          </Alert>
        )}
        <form onSubmit={form.onSubmit(handleLogin)}>
          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            required
            size="md"
            leftSection={<IconAt size="1.1rem" />}
            error={form.errors.email}
            {...form.getInputProps("email")}
          />

          <PasswordInput
            label="Password"
            placeholder="Your password"
            required
            mt="xl"
            size="md"
            leftSection={<IconLock size="1.1rem" />}
            error={form.errors.password}
            {...form.getInputProps("password")}
          />
          <Button
            fullWidth
            mt="xl"
            size="md"
            type="submit"
            loading={loading}
            leftSection={<IconLogin size="1.1rem" />}
          >
            Sign in
          </Button>
        </form>
      </Paper>

      <Text c="dimmed" size="sm" ta="center" mt={20}>
        By logging in, you agree to our Terms & Conditions.
      </Text>
    </Container>
  );
};

export default LoginPage;
