import { create } from "zustand";
import { Household } from "../../../../../types/Household";

export interface ScrStore {
  applicant: Household;
  setApplicant: (household: Household) => void;
  households: Household[];
  addHousehold: (household: Household) => void;
  editHousehold: (household: Household) => void;
  deleteHousehold: (id: number) => void;
}

export const useScrStore = create<ScrStore>()((set) => ({
  applicant: {} as Household, // Initialize applicant as an empty Household object
  setApplicant: (household: Household) =>
    set((state) => ({ ...state, applicant: household })),
  households: [],
  addHousehold: (household: Household) =>
    set((state) => ({
      ...state,
      households: [...state.households, household],
    })),
  editHousehold: (household: Household) =>
    set((state) => ({
      ...state,
      households: state.households.map((h) =>
        h.id === household.id ? household : h,
      ),
    })),
  deleteHousehold: (id: number) =>
    set((state) => ({
      ...state,
      households: state.households.filter((h) => h.id !== id),
    })),
}));
