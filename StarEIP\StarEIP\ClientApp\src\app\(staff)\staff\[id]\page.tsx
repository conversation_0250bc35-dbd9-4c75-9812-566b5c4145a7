"use client";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useScrStore } from "./ScrStore";
import HouseholdMemberForm from "../HouseholdMemberForm";
import Image from "next/image";
import InfoCard from "../InfoCard";
import <PERSON><PERSON>ogo from "../../../../../public/StarLogo.png";
import HouseholdMembersList from "../HouseholdMemebersList";
import { Backdrop, CircularProgress, Stack, Typography } from "@mui/material";
import axios from "axios";

const Page = () => {
  const { id } = useParams();
  const setApplicant = useScrStore((state) => state.setApplicant);
  const applicant = useScrStore((state) => state.applicant);

  const [loading, setLoading] = useState(true);
  const [isValid, setIsValid] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const validateId = async () => {
      try {
        const response = await axios.get(
          `https://localhost:7180/api/scr/${id}`,
        );
        if (response.status == 200) {
          setIsValid(true);
        } else {
          setErrorMessage("The code is invalid");
        }
      } catch (error) {
        setErrorMessage("An error occurred while validating the code");
      } finally {
        setLoading(false);
      }
    };

    validateId();
  }, [id]);

  if (loading) {
    return (
      <Backdrop sx={{ zIndex: (theme) => theme.zIndex.drawer + 1000 }} open>
        <Stack spacing={2}>
          <CircularProgress />
          <Typography variant="h6" component="span">
            Loading & Validating.....
          </Typography>
        </Stack>
      </Backdrop>
    );
  }

  if (!isValid) {
    return <div>{errorMessage}</div>;
  }

  return (
    <>
      <Image
        src={StarLogo}
        alt="Star Logo"
        style={{ alignSelf: "flex-start", width: "100px", height: "100px" }}
      />

      <InfoCard
        title="Your Information"
        subtitle="Applicant personal information"
        isComplete={(applicant.id || 0) > 0}
      >
        <HouseholdMemberForm
          household={applicant}
          isPersonal
          onClose={() => {}}
          onSave={(houseHold, remove) => {
            setApplicant(houseHold);
          }}
        />
      </InfoCard>

      <InfoCard
        title="Household Members"
        subtitle="Please list any members of your household above the age of 18"
        isComplete={false}
      >
        <HouseholdMembersList />
      </InfoCard>

      <InfoCard
        title="Current & Previose Adressess"
        subtitle="Please list all addresses you have lived at in the past 5 years"
        isComplete={false}
      >
        <HouseholdMembersList />
      </InfoCard>
    </>
  );
};

export default Page;
