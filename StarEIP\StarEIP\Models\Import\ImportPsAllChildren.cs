using System;

namespace StarEIP.Models.Import
{
    public class ImportPsAllChildren
    {
        public int Id { get; set; }
        
        public string ChildName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string ProgramId { get; set; }
        public string ChildStatus { get; set; }
        public string Region { get; set; }
        public string OngoingServiceCoordinator { get; set; }
        public DateTime? CurrentOcStartDate { get; set; }
        public DateTime? CurrentOcEndDate { get; set; }
        public string PrimaryLanguage { get; set; }
        public string Stage { get; set; }
        public DateTime? ProgramReferralDate { get; set; }
        public string Gender { get; set; }
        public DateTime? DateOfIntake { get; set; }
        public DateTime? DateOfFirstContact { get; set; }
        public string ReferralSource { get; set; }
        
        // Audit fields
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime LastImportedAt { get; set; }
    }
}
