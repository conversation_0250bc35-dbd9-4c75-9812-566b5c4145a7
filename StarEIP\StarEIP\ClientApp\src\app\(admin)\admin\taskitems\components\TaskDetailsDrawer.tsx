"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import TaskDetailsContent from "./TaskDetailsContent";
import { api } from "@/api/generated";
import {
  ChildDetailsDtoSchema,
  FullTaskDetailDtoSchema,
  TaskDetailsDtoSchema,
  TaskItemLinkSchema,
} from "@/api/types";

interface TaskDetailsDrawerProps {
  taskId: number | null;
  buttonText?: React.ReactNode;
  buttonSize?: "xs" | "sm" | "md" | "lg" | "xl";
  onTaskUpdated?: () => void;
  useHyperlink?: boolean;
}

const TaskDetailsDrawer: React.FC<TaskDetailsDrawerProps> = ({
  taskId,
  buttonText = "View Task",
  buttonSize = "xs",
  onTaskUpdated,
  useHyperlink = false,
}) => {
  const [opened, { open, close }] = useDisclosure(false);

  const [taskDetails, setTaskDetails] =
    useState<FullTaskDetailDtoSchema | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchTaskDetails = async () => {
    if (!taskId) return;

    setIsLoading(true);
    try {
      const response = await api.taskItems.getApiTaskItemsTaskId(taskId);
      setTaskDetails(response.data);
    } catch (error) {
      console.error("Error fetching task details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (opened && taskId) {
      fetchTaskDetails();
    }
  }, [opened, taskId]);

  const handleTaskUpdated = (updatedTask?: FullTaskDetailDtoSchema) => {
    if (updatedTask) {
      // If we received the updated task data, use it directly
      setTaskDetails(updatedTask);
    } else {
      // Otherwise, fetch the latest task details
      fetchTaskDetails();
    }

    if (onTaskUpdated) {
      onTaskUpdated();
    }
  };

  const isIcon = React.isValidElement(buttonText);
  return (
    <>
      {taskId && (
        <>
          {useHyperlink ? (
            <Text
              onClick={open}
              c="blue"
              style={{ textDecoration: "underline", cursor: "pointer" }}
              size={buttonSize}
            >
              {buttonText}
            </Text>
          ) : isIcon ? (
            <Button onClick={open} size={buttonSize} variant="subtle">
              {buttonText}
            </Button>
          ) : (
            <Button onClick={open} size={buttonSize}>
              {buttonText}
            </Button>
          )}

          <Drawer
            opened={opened}
            onClose={close}
            position="right"
            size="50%"
            padding="xs"
            styles={{
              content: { display: "flex", flexDirection: "column" },
              body: { flex: 1, overflow: "auto" },
            }}
            overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
            closeOnClickOutside
            title={
              taskDetails?.taskDetailsDto
                ? `Task #${taskDetails.taskDetailsDto.id}: ${taskDetails.taskDetailsDto.title}`
                : `Task #${taskId}`
            }
          >
            <TaskDetailsContent
              showTitle={false}
              fullTaskDetails={taskDetails}
              isLoading={isLoading}
              onTaskUpdated={handleTaskUpdated}
              useAbsoluteUrls={true}
            />
          </Drawer>
        </>
      )}
    </>
  );
};

export default TaskDetailsDrawer;
