using System.ComponentModel.DataAnnotations.Schema;

public class AuditLog
{
    public int Id { get; set; }
    public int? UserId { get; set; }
    public string? IpAddress { get; set; }
    public string TableName { get; set; }
    public int? PrimaryKey { get; set; }
    public string ColumnName { get; set; }
    [Column(TypeName = "nvarchar(max)")]
    public string? OldValue { get; set; }
    [Column(TypeName = "nvarchar(max)")]
    public string? NewValue { get; set; }
    public DateTime Timestamp { get; set; }
    public Guid? JwtGuid { get; set; }
    public string? UserAgent { get; set; }
    public string? PrimaryKeyString { get; set; }
}
