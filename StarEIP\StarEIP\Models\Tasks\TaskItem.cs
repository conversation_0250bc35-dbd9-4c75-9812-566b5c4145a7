using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models.Tasks
{
    public class TaskItem
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public int TaskTemplateId { get; set; }
        public int TaskStatusId { get; set; }
        public int? AssignedToUserId { get; set; }
        public int? ChildId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? DueAt { get; set; }


        public TaskStatus Status { get; set; } = default!;
        public Auth.ApplicationUser? AssignedToUser { get; set; }
        public TaskTemplate TaskTemplate { get; set; } = default!;
        [ForeignKey("ChildId")]
        public Child? Child { get; set; }
        public ICollection<TaskItemLink> Links { get; set; } = new List<TaskItemLink>();
    }
}