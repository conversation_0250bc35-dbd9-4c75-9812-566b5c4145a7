using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;

namespace StarEIP.Services.Auth;

public interface IUserService
{
    // Task<IEnumerable<ApplicationUser>> GetAllUsersAsync();
    Task<IEnumerable<ApplicationUser>> GetAllUsersAsync();
    IQueryable<ApplicationUser> GetAllUsersQueryable();
    Task<ApplicationUser?> GetUserByIdAsync(int id);
    Task<ApplicationUser?> GetUserByEmailAsync(string email);
    Task<IdentityResult> CreateUserAsync(ApplicationUser user, string password);
    Task<IdentityResult> CreateUserAsync(string email, string password, string firstName, string lastName);

    Task<IdentityResult> UpdateUserAsync(ApplicationUser user);
    Task<IdentityResult> DeleteUserAsync(ApplicationUser user);

    Task<IList<Claim>> GetUserClaimsAsync(ApplicationUser user);
    Task<IdentityResult> AddUserClaimAsync(ApplicationUser user, string claimType, string claimValue);
    Task<IdentityResult> RemoveUserClaimAsync(ApplicationUser user, string claimType, string claimValue);

    /*
     * TODO: retrieves all roles assigned to user
     * TODO: Retrieve all user permission
     * TODO: revoke user permission
     */
    Task<string> GenerateEmailConfirmationTokenAsync(ApplicationUser user);
}

public class UserService : IUserService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<UserService> _logger;

    public UserService(UserManager<ApplicationUser> userManager,
        ILogger<UserService> logger)
    {
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<IEnumerable<ApplicationUser>> GetAllUsersAsync()
    {
        return await _userManager.Users.ToListAsync();
    }

    public IQueryable<ApplicationUser> GetAllUsersQueryable()
    {
        return _userManager.Users;
    }

    public async Task<ApplicationUser?> GetUserByIdAsync(int id)
    {
        return await _userManager.Users.FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task<ApplicationUser?> GetUserByEmailAsync(string email)
    {
        return await _userManager.FindByEmailAsync(email);
    }

    public Task<IdentityResult> CreateUserAsync(ApplicationUser user, string password)
    {
        throw new NotImplementedException();
    }

    public async Task<IdentityResult> CreateUserAsync(string email, string password, string firstName, string lastName)
    {
        try
        {
            var user = new ApplicationUser
            {
                UserName = email,
                Email = email,
                FirstName = firstName,
                LastName = lastName
            };
            return await _userManager.CreateAsync(user, password);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error while creating user: {e.Message}");
            throw;
        }
    }

    public async Task<IdentityResult> UpdateUserAsync(ApplicationUser user)
    {
        try
        {
            return await _userManager.UpdateAsync(user);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error while updating user: {e.Message}");
            throw;
        }
    }

    public Task<IdentityResult> DeleteUserAsync(ApplicationUser user)
    {
        //TODO: decide soft delete or not 
        throw new NotImplementedException();
    }

    public async Task<IList<Claim>> GetUserClaimsAsync(ApplicationUser user)
    {
        try
        {
            return await _userManager.GetClaimsAsync(user);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error while getting user claims: {e.Message}");
            throw;
        }
    }

    public async Task<IdentityResult> AddUserClaimAsync(ApplicationUser user, string claimType, string claimValue)
    {
        try
        {
            return await _userManager.AddClaimAsync(user, new Claim(claimType, claimValue));
        }
        catch (Exception e)
        {
            _logger.LogError($"Error while adding user claim: {e.Message}");
            throw;
        }
    }

    public async Task<IdentityResult> RemoveUserClaimAsync(ApplicationUser user, string claimType, string claimValue)
    {
        try
        {
            var claims = await _userManager.GetClaimsAsync(user);
            var claim = claims.FirstOrDefault(c => c.Type == claimType && c.Value == claimValue);
            if (claim != null)
                return await _userManager.RemoveClaimAsync(user, claim);

            return IdentityResult.Failed(new IdentityError { Description = "Claim not found" });
        }
        catch (Exception e)
        {
            _logger.LogError($"Error while removing user claim: {e.Message}");
            throw;
        }
    }


    public async Task<string> GenerateEmailConfirmationTokenAsync(ApplicationUser user)
    {
        try
        {
            return await _userManager.GenerateEmailConfirmationTokenAsync(user);
        }
        catch (Exception e)
        {
            _logger.LogError($"Error while generating email confirmation token: {e.Message}");
            throw;
        }
    }
}