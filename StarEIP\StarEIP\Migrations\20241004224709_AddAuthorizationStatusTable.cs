﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddAuthorizationStatusTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AuthorizationStatus",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuthorizationStatus", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AuthorizationStatus",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { "New", "New" },
                    { "Active", "Active" },
                    { "Closed", "Closed" }
                });

            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Authorization",
                newName: "StatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_Authorization_AuthorizationStatus_StatusId",
                table: "Authorization",
                column: "StatusId",
                principalTable: "AuthorizationStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Authorization_AuthorizationStatus_StatusId",
                table: "Authorization");

            migrationBuilder.DropTable(
                name: "AuthorizationStatus");

            migrationBuilder.RenameColumn(
                name: "StatusId",
                table: "Authorization",
                newName: "Status");

            migrationBuilder.AlterColumn<string>(
                name: "Status",
                table: "Authorization",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);
        }

    }
}
