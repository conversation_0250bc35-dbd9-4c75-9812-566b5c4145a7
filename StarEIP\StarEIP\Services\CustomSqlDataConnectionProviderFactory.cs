using DevExpress.DataAccess.Sql;
using DevExpress.DataAccess.ConnectionParameters;
using DevExpress.DataAccess.Web;
using DevExpress.DataAccess.Wizard.Services;
using System.Data.SqlClient;

namespace StarEIP.Services
{
    // Custom factory implementing IConnectionProviderFactory
    public class CustomSqlDataConnectionProviderFactory : IConnectionProviderFactory
    {
        private readonly IConfiguration _configuration;

        public CustomSqlDataConnectionProviderFactory(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        // Creates an instance of the connection provider service
        public IConnectionProviderService Create()
        {
            return new CustomSqlDataConnectionProviderService(_configuration);
        }
    }

    // Custom connection provider service implementing IConnectionProviderService
    public class CustomSqlDataConnectionProviderService : IConnectionProviderService
    {
        private readonly IConfiguration _configuration;

        public CustomSqlDataConnectionProviderService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        // Loads the SqlDataConnection based on the connection name
        public SqlDataConnection LoadConnection(string connectionName)
        {
            if (string.IsNullOrWhiteSpace(connectionName))
                throw new ArgumentException("Connection name cannot be null or empty.", nameof(connectionName));

            var connectionString = _configuration.GetConnectionString(connectionName);
            if (string.IsNullOrEmpty(connectionString))
                throw new InvalidOperationException($"Connection string '{connectionName}' not found.");

            var builder = new SqlConnectionStringBuilder(connectionString);

            var parameters = new MsSqlConnectionParameters(
                serverName: builder.DataSource,
                databaseName: builder.InitialCatalog,
                userName: builder.IntegratedSecurity ? null : builder.UserID,
                password: builder.IntegratedSecurity ? null : builder.Password,
                authorizationType: builder.IntegratedSecurity
                    ? MsSqlAuthorizationType.Windows
                    : MsSqlAuthorizationType.SqlServer,
                encrypt: DevExpress.Utils.DefaultBoolean.Default,
                trustServerCertificate: builder.TrustServerCertificate ? DevExpress.Utils.DefaultBoolean.True : DevExpress.Utils.DefaultBoolean.Default
            );

            return new SqlDataConnection(connectionName, parameters);
        }
    }
}
