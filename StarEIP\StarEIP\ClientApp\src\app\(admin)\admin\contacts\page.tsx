"use client";

import { useState, useRef, useEffect } from "react";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Editing,
  Toolbar,
  Item,
  Button as Dg<PERSON><PERSON>on,
} from "devextreme-react/data-grid";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { useRouter } from "next/navigation";
import dataGridExport from "@/app/Utils/dataGridExport";
import Button from "devextreme-react/button";

const serviceUrl = urlHelpers.getAbsoluteURL("api/contacts");

const Page = () => {
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const router = useRouter();
  const dataGridRef = useRef(null);

  const loadContactsDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        updateUrl: `${serviceUrl}/update`,
        insertUrl: `${serviceUrl}/create`,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  };

  useEffect(() => {
    loadContactsDataSource();
  }, []);

  const handleFocusedRowChanging = (e: any) => {
    // setSelectedChild(e.row && e.row.data);
    // if (onFocusedChildChanging) {
    //     onFocusedChildChanging(e.row.data);
    // }
  };

  return (
    <>
      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onFocusedRowChanged={handleFocusedRowChanging}
      >
        <Toolbar>
          <Item location="before">
            <Button icon="refresh" onClick={loadContactsDataSource} />
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item name="addRowButton" locateInMenu="auto" location="before" />
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item name="applyFilterButton" locateInMenu="auto" location="after" />
          <Item name="revertButton" locateInMenu="auto" location="after" />
          <Item name="saveButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <Editing allowUpdating allowAdding mode="popup" />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <ColumnFixing />
        <Paging defaultPageSize={40} />
        <Pager showPageSizeSelector />
        <FilterPanel visible />
        <FilterBuilderPopup />
        <Export enabled={true} />

        <Column type="buttons" cssClass={"text-align-right"}>
          <DgButton name="edit" />
        </Column>
        <Column
          dataField="id"
          caption="ID"
          allowEditing={false}
          visible={false}
        />
        <Column dataField="name" caption="Name" />
        <Column dataField="phoneNumber" caption="Phone Number" />
        <Column dataField="email" caption="Email" />
        <Column dataField="faxNumber" caption="Fax Number" />
      </DataGrid>
    </>
  );
};

export default Page;
