{"name": "clientapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "api-client:generate": "tsx src/api/generate-api-client.ts", "api-client:update": "tsx src/api/generate-api-client.ts --update", "postinstall": "npm run api-client:generate"}, "dependencies": {"@devexpress/analytics-core": "^24.2.7", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mantine/carousel": "^8.0.0", "@mantine/charts": "^8.0.0", "@mantine/code-highlight": "^8.0.0", "@mantine/core": "^8.0.0", "@mantine/dates": "^8.0.0", "@mantine/dropzone": "^8.0.0", "@mantine/form": "^8.0.0", "@mantine/hooks": "^8.0.0", "@mantine/modals": "^8.0.0", "@mantine/notifications": "^8.0.0", "@mantine/nprogress": "^8.0.0", "@mantine/spotlight": "^8.0.0", "@mantine/tiptap": "^8.0.0", "@microsoft/clarity": "^1.0.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/material-nextjs": "^7.1.0", "@tabler/icons-react": "^3.31.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "devexpress-reporting": "^24.2.7", "devexpress-reporting-react": "^24.2.7", "devextreme": "24.2.7", "devextreme-aspnet-data-nojquery": "^5.1.0", "devextreme-react": "24.2.7", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "es-toolkit": "^1.37.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "next": "^15.3.2", "patch-package": "^8.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-google-autocomplete": "^2.7.5", "react-icons": "^5.5.0", "react-signature-pad-wrapper": "^4.1.0", "recharts": "^2.15.3", "sass": "^1.88.0", "sharp": "^0.34.1", "zustand": "^5.0.4"}, "devDependencies": {"@types/node": "22.15.17", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.32.1", "eslint": "^9", "eslint-config-next": "^15.3.2", "orval": "^7.9.0", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tsx": "^4.19.4", "typescript": "^5"}, "overrides": {"rimraf": "3.0.2"}}