using CsvHelper.Configuration.Attributes;
using System;

namespace StarEIP.Models.Import
{
    public class PsAllChildrenCsv
    {
        [Name("Child's Name")]
        public string ChildName { get; set; }

        [Name("Date Of Birth")]
        public DateTime? DateOfBirth { get; set; }

        [Name("Address")]
        public string Address { get; set; }

        [Name("Phone #")]
        public string Phone { get; set; }

        [Name("City")]
        public string City { get; set; }

        [Name("State")]
        public string State { get; set; }

        [Name("Zip")]
        public string Zip { get; set; }

        [Name("Program ID")]
        public string ProgramId { get; set; }

        [Name("Child Status")]
        public string ChildStatus { get; set; }

        [Name("Region")]
        public string Region { get; set; }

        [Name("Ongoing Service Coordinator")]
        public string OngoingServiceCoordinator { get; set; }

        [Name("Current OC Start Date")]
        public DateTime? CurrentOcStartDate { get; set; }

        [Name("Current OC End Date")]
        public DateTime? CurrentOcEndDate { get; set; }

        [Name("Primary Language")]
        public string PrimaryLanguage { get; set; }

        [Name("Stage")]
        public string Stage { get; set; }

        [Name("Program Referral Date")]
        public DateTime? ProgramReferralDate { get; set; }

        [Name("Gender")]
        public string Gender { get; set; }

        [Name("Date of Intake")]
        public DateTime? DateOfIntake { get; set; }

        [Name("Date of First Contact")]
        public DateTime? DateOfFirstContact { get; set; }

        [Name("Referral Source")]
        public string ReferralSource { get; set; }
    }
}
