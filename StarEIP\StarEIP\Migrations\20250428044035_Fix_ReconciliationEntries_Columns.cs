﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class Fix_ReconciliationEntries_Columns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReconciliationEntries_Date_EiNumber_Service_Provider_StartTime_EndTime",
                table: "ReconciliationEntries");

            migrationBuilder.DropColumn(
                name: "CheckAmount",
                table: "ReconciliationEntries");

            migrationBuilder.DropColumn(
                name: "CheckDate",
                table: "ReconciliationEntries");

            migrationBuilder.DropColumn(
                name: "EndTime",
                table: "ReconciliationEntries");

            migrationBuilder.DropColumn(
                name: "StartTime",
                table: "ReconciliationEntries");

            migrationBuilder.RenameColumn(
                name: "Units",
                table: "ReconciliationEntries",
                newName: "PaymentAmount");

            migrationBuilder.RenameColumn(
                name: "Total",
                table: "ReconciliationEntries",
                newName: "Discrepancy");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "ReconciliationEntries",
                newName: "SubType");

            migrationBuilder.RenameColumn(
                name: "ServiceDate",
                table: "ReconciliationEntries",
                newName: "RemittDate");

            migrationBuilder.RenameColumn(
                name: "Service",
                table: "ReconciliationEntries",
                newName: "RemittStatus");

            migrationBuilder.RenameColumn(
                name: "Rate",
                table: "ReconciliationEntries",
                newName: "BillAmount");

            migrationBuilder.RenameColumn(
                name: "Payor",
                table: "ReconciliationEntries",
                newName: "Reconciled");

            migrationBuilder.RenameColumn(
                name: "PaymentType",
                table: "ReconciliationEntries",
                newName: "FundingSource");

            migrationBuilder.RenameColumn(
                name: "InvoiceNumber",
                table: "ReconciliationEntries",
                newName: "ChildName");

            migrationBuilder.RenameColumn(
                name: "Date",
                table: "ReconciliationEntries",
                newName: "SessionDate");

            migrationBuilder.RenameColumn(
                name: "ClientName",
                table: "ReconciliationEntries",
                newName: "AdjustReasonDescription");

            migrationBuilder.RenameColumn(
                name: "CheckNumber",
                table: "ReconciliationEntries",
                newName: "AdjustReasonCodes");

            migrationBuilder.AddColumn<DateTime>(
                name: "ClaimCreated",
                table: "ReconciliationEntries",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.CreateIndex(
                name: "IX_ReconciliationEntries_EiNumber_Provider_SessionDate_Type_SubType",
                table: "ReconciliationEntries",
                columns: new[] { "EiNumber", "Provider", "SessionDate", "Type", "SubType" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReconciliationEntries_EiNumber_Provider_SessionDate_Type_SubType",
                table: "ReconciliationEntries");

            migrationBuilder.DropColumn(
                name: "ClaimCreated",
                table: "ReconciliationEntries");

            migrationBuilder.RenameColumn(
                name: "SubType",
                table: "ReconciliationEntries",
                newName: "Status");

            migrationBuilder.RenameColumn(
                name: "SessionDate",
                table: "ReconciliationEntries",
                newName: "Date");

            migrationBuilder.RenameColumn(
                name: "RemittStatus",
                table: "ReconciliationEntries",
                newName: "Service");

            migrationBuilder.RenameColumn(
                name: "RemittDate",
                table: "ReconciliationEntries",
                newName: "ServiceDate");

            migrationBuilder.RenameColumn(
                name: "Reconciled",
                table: "ReconciliationEntries",
                newName: "Payor");

            migrationBuilder.RenameColumn(
                name: "PaymentAmount",
                table: "ReconciliationEntries",
                newName: "Units");

            migrationBuilder.RenameColumn(
                name: "FundingSource",
                table: "ReconciliationEntries",
                newName: "PaymentType");

            migrationBuilder.RenameColumn(
                name: "Discrepancy",
                table: "ReconciliationEntries",
                newName: "Total");

            migrationBuilder.RenameColumn(
                name: "ChildName",
                table: "ReconciliationEntries",
                newName: "InvoiceNumber");

            migrationBuilder.RenameColumn(
                name: "BillAmount",
                table: "ReconciliationEntries",
                newName: "Rate");

            migrationBuilder.RenameColumn(
                name: "AdjustReasonDescription",
                table: "ReconciliationEntries",
                newName: "ClientName");

            migrationBuilder.RenameColumn(
                name: "AdjustReasonCodes",
                table: "ReconciliationEntries",
                newName: "CheckNumber");

            migrationBuilder.AddColumn<decimal>(
                name: "CheckAmount",
                table: "ReconciliationEntries",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CheckDate",
                table: "ReconciliationEntries",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<TimeSpan>(
                name: "EndTime",
                table: "ReconciliationEntries",
                type: "time",
                nullable: false,
                defaultValue: new TimeSpan(0, 0, 0, 0, 0));

            migrationBuilder.AddColumn<TimeSpan>(
                name: "StartTime",
                table: "ReconciliationEntries",
                type: "time",
                nullable: false,
                defaultValue: new TimeSpan(0, 0, 0, 0, 0));

            migrationBuilder.CreateIndex(
                name: "IX_ReconciliationEntries_Date_EiNumber_Service_Provider_StartTime_EndTime",
                table: "ReconciliationEntries",
                columns: new[] { "Date", "EiNumber", "Service", "Provider", "StartTime", "EndTime" },
                unique: true);
        }
    }
}
