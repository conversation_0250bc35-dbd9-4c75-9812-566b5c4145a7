﻿using System.Text.Json;
using System.Text.Json.Serialization;
using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using StarEIP.Models;
using Microsoft.EntityFrameworkCore;

namespace StarEIP.Controllers
{
    [Route("api/physicians")]
    [ApiController]
    [Authorize]
    public class PhysicianController(StarEipDbContext dbContext, ILogger<PhysicianController> logger) : ControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var physicians = dbContext.Physicians;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = [nameof(Physician.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(physicians, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get users");
                throw;
            }
        }

        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromForm] int key, [FromForm] string values)
        {
            var staff = await dbContext.Physicians.SingleAsync(r => r.Id == key);
            JsonConvert.PopulateObject(values, staff);
            await dbContext.SaveChangesAsync();
            return Ok(staff);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromForm] string values)
        {
            var physician = new Physician();
            JsonConvert.PopulateObject(values, physician);

            if (string.IsNullOrEmpty(physician.Name))
            {
                return BadRequest("EmailTemplate Name is required.");
            }

            await dbContext.Physicians.AddAsync(physician);
            await dbContext.SaveChangesAsync();
            return Ok(physician);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetPhysicianById(int id)
        {
            try
            {
                var physician = await dbContext.Physicians.FindAsync(id);
                if (physician == null)
                {
                    return NotFound();
                }
                return Ok(physician);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get physician by ID");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
