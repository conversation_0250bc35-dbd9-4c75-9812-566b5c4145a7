﻿using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;

namespace StarEIP.Services
{
    public class DocumentStorageService
    {
        private readonly StarEipDbContext dbContext;
        private readonly ILogger<DocumentStorageService> logger;
        private readonly BlobServiceClient blobServiceClient;

        public DocumentStorageService(StarEipDbContext dbContext, ILogger<DocumentStorageService> logger, BlobServiceClient blobServiceClient)
        {
            this.dbContext = dbContext;
            this.logger = logger;
            this.blobServiceClient = blobServiceClient;
        }

        public async Task SaveDocument(int childId, Stream stream, string fileName, string fileType)
        {
            try
            {
                var containerClient = blobServiceClient.GetBlobContainerClient("child-attachments");
                await containerClient.CreateIfNotExistsAsync();
                var blobName = $"{childId}/{fileName}";
                var blobClient = containerClient.GetBlobClient(blobName);
                int version = 1;

                while (await blobClient.ExistsAsync())
                {
                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                    var fileExtension = Path.GetExtension(fileName);
                    blobName = $"{childId}/{fileNameWithoutExtension}_v{version}{fileExtension}";
                    blobClient = containerClient.GetBlobClient(blobName);
                    version++;
                }

                await blobClient.UploadAsync(stream, overwrite: true);

                var attachment = new ChildAttachment
                {
                    ChildId = childId,
                    FileName = Path.GetFileName(blobName),
                    FilePath = blobName,
                    FileType = fileType
                };

                dbContext.ChildAttachments.Add(attachment);
                await dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error uploading file for child ID {childId}", childId);
                throw;
            }
        }

    }
}
