"use client";

import { Container } from "@mantine/core";
import { useRouter } from "next/navigation";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import PatientForm from "@/app/(admin)/admin/patients/PatientForm";
import { useEffect, useState } from "react";
import { usePatientStore } from "../PatientStore";
import { ChildDetailsDtoSchema } from "@/api/types";

const physicianServiceUrl = urlHelpers.getAbsoluteURL("api/physicians");

const NewPatientPage = () => {
  const [physicians, setPhysicians] = useState<any[]>([]);
  const { dataGridRef } = usePatientStore();

  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const physiciansResponse = await axios.get(`${physicianServiceUrl}`);
        setPhysicians(physiciansResponse.data.data);
      } catch (e) {
        console.error("Error fetching child data:", e);
        router.push("/admin/patients");
      }
    };
    fetchData();
  }, [router]);

  const handleCancel = () => {
    router.push("/admin/patients");
  };

  const handleSubmit = async (child: ChildDetailsDtoSchema) => {
    try {
      const url = urlHelpers.getAbsoluteURL("api/children/create");
      const response = await axios.post(url, child);
      if (response.status === 200) {
        const grid = dataGridRef?.current?.instance?.();
        if (grid) {
          console.log("grid have value ");
          grid.getDataSource().reload();
        }
        router.push("/admin/patients");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <Container p="lg" fluid style={{ flex: 1, height: "100%" }}>
      <PatientForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isEditMode={false}
        physicians={physicians}
      />
    </Container>
  );
};
export default NewPatientPage;
