﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddFaxIdx : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Faxes_PhysicianId",
                table: "Faxes",
                column: "PhysicianId");

            migrationBuilder.AddForeignKey(
                name: "FK_Faxes_Physician_PhysicianId",
                table: "Faxes",
                column: "PhysicianId",
                principalTable: "Physician",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Faxes_Physician_PhysicianId",
                table: "Faxes");

            migrationBuilder.DropIndex(
                name: "IX_Faxes_PhysicianId",
                table: "Faxes");
        }
    }
}
