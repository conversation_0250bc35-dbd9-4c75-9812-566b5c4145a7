using StarEIP.Models.Auth;
using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models
{
    [Table("Authorization")]
    public class Authorization
    {
        public int Id { get; set; }
        public int ChildId { get; set; }
        public string StatusId { get; set; } = "New";
        public string? AuthType { get; set; }
        public int UserId { get; set; }
        public string? AuthNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? Units { get; set; }

        public int? ScStatus { get; set; }
        public DateTime? StatusLastUpdated { get; set; }
        public DateTime? FollowUpDate { get; set; }
        public DateTime? ScStatusLastUpdated { get; set; }

        public virtual Child? Child { get; set; }

        [ForeignKey("UserId")]
        public virtual ApplicationUser? User { get; set; }

        [ForeignKey("StatusId")]
        public virtual AuthorizationStatus? Status { get; set; }

        [ForeignKey("ScStatus")]
        public virtual ScStatusType? ScStatusType { get; set; }
    }
}
