// components/shared/EntityAttachments/ShareModal.tsx
import React, { useEffect, useState } from "react";
import {
  Modal,
  Select,
  Button,
  Progress,
  Stack,
  Text,
  Group,
  Loader,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { IconCheck } from "@tabler/icons-react";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { Contact } from "../../../../../types/Attachment";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShare: (contactId: string, method: string) => Promise<void>;
  isSharing: boolean;
  shareMethod: string;
}

const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  onShare,
  isSharing,
  shareMethod,
}) => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<string | null>(null);
  const [shareProgress, setShareProgress] = useState(0);
  const [isLoadingContacts, setIsLoadingContacts] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchContacts();
    }
  }, [isOpen]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedContact(null);
      setShareProgress(0);
    }
  }, [isOpen]);

  const fetchContacts = async () => {
    setIsLoadingContacts(true);
    try {
      const response = await axios.get(
        `${urlHelpers.getAbsoluteURL("api/contacts/list")}`,
      );

      if (Array.isArray(response.data)) {
        const validContacts = response.data.filter(
          (contact) =>
            contact &&
            typeof contact.id !== "undefined" &&
            typeof contact.name === "string",
        );
        setContacts(validContacts);
      }
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Failed to fetch contacts",
        color: "red",
      });
    } finally {
      setIsLoadingContacts(false);
    }
  };

  const handleShare = async () => {
    if (!selectedContact) {
      notifications.show({
        message: "Please select both contact and share method",
        color: "red",
      });
      return;
    }

    // Start progress animation
    setShareProgress(0);
    const progressInterval = setInterval(() => {
      setShareProgress((prev) => {
        if (prev >= 90) return 90;
        return prev + 10;
      });
    }, 300);

    try {
      await onShare(selectedContact, shareMethod);
      clearInterval(progressInterval);
      setShareProgress(100);
      notifications.show({
        title: "Success",
        message: "File shared successfully",
        color: "green",
        icon: <IconCheck size={18} />,
      });

      setTimeout(() => {
        onClose();
        setSelectedContact(null);
        setShareProgress(0);
      }, 1000);
    } catch (error) {
      clearInterval(progressInterval);
      setShareProgress(0);
    }
  };

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      title={`Share via ${shareMethod}`}
      size="md"
    >
      <Stack gap="md">
        <Select
          data={contacts.map((contact) => ({
            value: contact.id.toString(),
            label: contact.name,
            disabled: !contact.email && !contact.faxNumber,
          }))}
          label="Select Contact"
          placeholder="Choose a contact"
          value={selectedContact}
          onChange={(value) => {
            setSelectedContact(value);
          }}
          disabled={isSharing || isLoadingContacts}
          searchable
          clearable
          required
        />

        {isLoadingContacts && <Loader size="sm" />}

        {/*<Select*/}
        {/*    data={getShareMethods()}*/}
        {/*    label="Share Method"*/}
        {/*    placeholder="Choose share method"*/}
        {/*    value={sharedMethod}*/}
        {/*    onChange={setSharedMethod}*/}
        {/*    disabled={isSharing || !selectedContact}*/}
        {/*    required*/}
        {/*/>*/}

        {selectedContact &&
          contacts.find((c) => c.id.toString() === selectedContact) && (
            <Group gap="xs">
              <Text size="sm">Selected Contact:</Text>
              <Text size="sm">
                {
                  contacts.find((c) => c.id.toString() === selectedContact)
                    ?.name
                }
              </Text>
            </Group>
          )}

        {isSharing && (
          <Stack gap="xs">
            <Text size="sm" mb={4}>
              Sharing in progress...
            </Text>
            <Progress
              value={shareProgress}
              size="xl"
              radius="xl"
              animated
              // label={`${shareProgress}%`}
              color={shareProgress === 100 ? "green" : "blue"}
            />
          </Stack>
        )}

        <Button
          onClick={handleShare}
          disabled={isSharing || !selectedContact}
          loading={isSharing}
          fullWidth
        >
          {isSharing ? "Sharing..." : "Share"}
        </Button>
      </Stack>
    </Modal>
  );
};

export default ShareModal;
