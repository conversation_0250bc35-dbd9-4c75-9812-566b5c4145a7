"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import axios from "axios";
import { notifications } from "@mantine/notifications";
import {
  Box,
  Button,
  Group,
  Paper,
  Stack,
  Text,
  Textarea,
} from "@mantine/core";
import urlHelpers from "@/app/urlHelpers";
import { SmsMessage } from "../../../../../types/SmsMessage";
import { IconRefresh, IconSend } from "@tabler/icons-react";

const serviceUrl = urlHelpers.getAbsoluteURL("api/sms");

interface SmsSectionProps {
  phoneNumber: string | null;
  childId: number;
}

export const SmsSection: React.FC<SmsSectionProps> = ({
  phoneNumber,
  childId,
}) => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<SmsMessage[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messageEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () =>
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const fetchMessages = useCallback(async () => {
    if (!childId) return;

    setIsLoading(true);
    try {
      const response = await axios.get(`${serviceUrl}/messages/${childId}`);
      setMessages(response.data);
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Failed to fetch messages",
        color: "red",
      });
    } finally {
      setIsLoading(false);
    }
  }, [childId]);

  useEffect(() => {
    fetchMessages();
  }, [fetchMessages]);

  const handleSend = async () => {
    if (!message.trim()) return;

    setIsSending(true);
    try {
      await axios.post(`${serviceUrl}/send`, {
        to: phoneNumber,
        message: message.trim(),
        childId: childId,
      });

      await fetchMessages();

      notifications.show({
        title: "Sucess",
        message: "SMS sent successfully",
        color: "green",
      });

      setMessage("");
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Failed to send SMS",
        color: "red",
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Stack
      gap="sm"
      style={{ height: "100%", display: "flex", flexDirection: "column" }}
    >
      <Text size="sm" c="dimmed">
        Messages with {phoneNumber}
      </Text>

      <Stack
        p="sm"
        align="stretch"
        gap="md"
        style={{ height: "100%", overflow: "auto" }}
      >
        {messages.map((message) => (
          <Group
            key={message.id}
            justify={message.direction === "out" ? "end" : "start"}
            w="100%"
          >
            <Box style={{ maxWidth: "70%" }}>
              <Box
                style={{
                  backgroundColor:
                    message.direction === "out" ? "#f6eded" : "#f0f0f0",
                  padding: "12px 16px",
                  borderRadius: "18px",
                  borderBottomRightRadius:
                    message.direction === "out" ? "4px" : "18px",
                  borderBottomLeftRadius:
                    message.direction === "in" ? "4px" : "18px",
                }}
              >
                <Text size="sm">{message.text}</Text>
              </Box>
              <Text
                size="xs"
                c="dimmed"
                mt={4}
                ta={message.direction === "out" ? "right" : "left"}
              >
                {new Date(message.createdAt).toLocaleString()}
              </Text>
            </Box>
          </Group>
        ))}
        <div ref={messageEndRef}></div>
      </Stack>

      <Paper shadow="sm" p="xs" withBorder>
        <Group align="flex-end" gap="xs">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.currentTarget.value)}
            placeholder="Type a message..."
            style={{ flex: 1 }}
            autosize
            minRows={2}
            maxRows={4}
            size="sm"
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                if (message.trim() && !isSending) {
                  handleSend();
                }
              }
            }}
          />
          <Button
            disabled={!message.trim() || isSending}
            loading={isSending}
            onClick={handleSend}
            variant="light"
            size="sm"
            color="blue"
            p="xs"
            style={{ minWidth: "unset" }}
          >
            <IconSend size={20} stroke={1.5} />
          </Button>
        </Group>
      </Paper>
    </Stack>
  );
};
