﻿using System.Text.Json.Serialization;

namespace StarEIP.Models.App.TelebroadApi
{
    public class TelebroadFaxMessages
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("time")]
        [<PERSON><PERSON><PERSON>onverter(typeof(StringToLongConverter))]
        public long Time { get; set; }

        [Json<PERSON>onverter(typeof(StringToIntConverter))]
        [JsonPropertyName("pages")]
        public int Pages { get; set; }

        [JsonPropertyName("mailbox")]
        public string Mailbox { get; set; }

        [JsonPropertyName("called")]
        public string Called { get; set; }

        [Json<PERSON>ropertyName("caller")]
        public string Caller { get; set; }

        [Json<PERSON>ropertyName("callerid")]
        public string CallerId { get; set; }

        [Json<PERSON>ropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("dir")]
        public string Directory { get; set; }

        [Json<PERSON>ropertyName("sent_by")]
        public string SentBy { get; set; }

        [JsonPropertyName("read_by")]
        public string ReadBy { get; set; }

        [Json<PERSON>ropertyName("seen")]
        public string Seen { get; set; }
    }

    public class TelebroadFaxMessage
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("data")]
        public string Data { get; set; }
    }
}
