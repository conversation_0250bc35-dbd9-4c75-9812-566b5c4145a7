﻿namespace StarEIP.Models.Tasks
{
    public class EmailMessage
    {
        public int Id { get; set; }

        public string GraphMessageId { get; set; } = string.Empty;
        public string InternetMessageId { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string? SenderEmail { get; set; }
        public DateTime ReceivedAt { get; set; }
        public string WebLink { get; set; } = string.Empty; // deep link to view in Outlook

        public ICollection<TaskItemEmail> LinkedTasks { get; set; } = new List<TaskItemEmail>();
    }
}
