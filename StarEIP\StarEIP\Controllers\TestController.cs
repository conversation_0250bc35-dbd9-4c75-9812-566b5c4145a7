
using Microsoft.AspNetCore.Mvc;
using StarEIP.Services.Core;

namespace StarEIP.Controllers
{
    [ApiController]
    [Route("api/test")]
    public class TestController : ControllerBase
    {
        private readonly EmailService emailService;

        public TestController(EmailService emailService)
        {
            this.emailService = emailService;
        }

        [HttpPost]
        public async Task<IActionResult> SendTestEmail()
        {
            try
            {
                await emailService.SendAsync("<EMAIL>", "<EMAIL>", "test email", "This is a test email.");

                return Ok("Test email sent successfully.");
            }
            catch (Exception ex)
            {
                // Handle any exceptions that occur during email sending
                return StatusCode(500, $"Failed to send test email: {ex.Message}");
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetEmails([FromQuery] string email,[FromQuery] string? filterEmail = null)
        {
            var emails = await emailService.GetLastTenEmailsAsync(email, filterEmail);
            return Ok(emails);
        }
    }
}