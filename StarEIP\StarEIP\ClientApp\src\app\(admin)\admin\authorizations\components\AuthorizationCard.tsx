import {
  Authorization,
  AuthorizationWithChild,
} from "../../../../../../types/Authorization";
import { Badge, Card, Group, Stack, Text } from "@mantine/core";
import React from "react";

interface AuthorizationCard {
  authorization: Authorization;
  showStatus: boolean;
}

const AuthorizationCard: React.FC<AuthorizationCard> = ({
  authorization,
  showStatus = true,
}) => {
  const InfoGroup: React.FC<{ label: string; value: string | undefined }> = ({
    label,
    value,
  }) => (
    <Group>
      <Text fw={400} size="14px" c="dimmed">
        {label}:
      </Text>
      <Text fw={500} size="14px" c="rgba(0, 0, 0, 0.44)">
        {value}
      </Text>
    </Group>
  );

  return (
    <Card shadow="xs" padding="md" radius="md">
      <Group justify="space-between">
        {showStatus && (
          <Text fw={500} size="14px">
            Auth #{authorization.authNumber}
          </Text>
        )}
        {showStatus && <Badge>{authorization.statusId}</Badge>}
      </Group>
      <Stack gap="xs">
        <InfoGroup label="SC" value={authorization.userFullName} />
        <InfoGroup label="Type" value={authorization.authType} />
        {authorization.startDate && authorization.endDate && (
          <InfoGroup
            label="Date"
            value={`${new Date(authorization.startDate).toLocaleDateString("en-US")} - ${new Date(authorization.endDate).toLocaleDateString("en-US")}`}
          />
        )}
        {authorization.units && (
          <InfoGroup label="Units" value={authorization.units.toString()} />
        )}
      </Stack>
    </Card>
  );
};

export default AuthorizationCard;
