.control {
  font-weight: 500;
  display: block;
  width: 100%;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  color: var(--mantine-color-white);
  font-size: var(--mantine-font-size-sm);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--mantine-color-white);
  }
}

.link {
  font-weight: 500;
  display: block;
  text-decoration: none;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  padding-left: var(--mantine-spacing-md);
  margin-left: var(--mantine-spacing-xl);
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-white);
  border-left: 1px solid var(--mantine-color-gray-3);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--mantine-color-white);
  }
}

.chevron {
  transition: transform 200ms ease;
}
