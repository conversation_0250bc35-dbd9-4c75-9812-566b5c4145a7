﻿using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using SkiaSharp;
using StarEIP.Services.Data;

namespace StarEIP.Controllers
{
    [Route("api/children")]
    [ApiController]
    [Authorize]
    public class ChildrenController : ControllerBase
    {
        private readonly ILogger<ChildrenController> _logger;
        private readonly StarEipDbContext _dbContext;
        private readonly ChildDataService childDataService;

        public ChildrenController(ILogger<ChildrenController> logger, StarEipDbContext dbContext, ChildDataService childDataService)
        {
            _logger = logger;
            _dbContext = dbContext;
            this.childDataService = childDataService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var children = childDataService.GetChildrenDto();
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                _dbContext.ChangeTracker.LazyLoadingEnabled = true;
                _dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = new[] { nameof(Child.Id) };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync<ChildDetailsDto>(children, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get staff");
                throw;
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ChildDetailsDto>> GetById(int id)
        {
            try
            {
                var childDetails = await childDataService.GetChildrenDto().FirstOrDefaultAsync(c => c.Id == id);
                if (childDetails == null)
                    return NotFound();

                return Ok(childDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get staff by id");
                throw;
            }
        }

        [HttpGet("Status")]
        public async Task<ActionResult<List<ChildStatus>>> GetStatus()
        {
            var status = await _dbContext.ChildStatuses.ToListAsync();
            return Ok(status);
        }

        public record ChildStatusUpdateRequest(int ChildId, string Status);

        [HttpPost("status")]
        public async Task<ActionResult<ChildDetailsDto>> UpdateStatus([FromBody] ChildStatusUpdateRequest request)
        {
            var child = await _dbContext.Children.FirstOrDefaultAsync(c => c.Id == request.ChildId);

            if (child == null)
            {
                return NotFound();
            }

            child.Status = request.Status;
            child.StatusLastUpdated = DateTime.Now;
            await _dbContext.SaveChangesAsync();

            var childDetails = await childDataService.GetChildrenDto().FirstOrDefaultAsync(c => c.Id == request.ChildId);
            return Ok(childDetails);
        }

        [HttpPut("Update")]
        public async Task<ActionResult<Child>> Update([FromForm] int key, [FromForm] string values)
        {
            var child = await _dbContext.Children.SingleAsync(r => r.Id == key);
            var previousStatus = child.Status;
            JsonConvert.PopulateObject(values, child);

            if (child.Status != previousStatus)
            {
                child.StatusLastUpdated = DateTime.Now;
            }

            await _dbContext.SaveChangesAsync();
            return Ok(child);
        }

        [HttpPost("create")]
        public async Task<ActionResult<Child>> Create([FromBody] Child child)
        {
            child.Status ??= "New";
            child.StatusLastUpdated = DateTime.Now;
            _dbContext.Children.Add(child);
            await _dbContext.SaveChangesAsync();
            return Ok(child);
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> Delete([FromForm] int key)
        {
            _logger.LogInformation("Entering Delete Child");
            try
            {
                var child = await _dbContext.Children.SingleAsync(r => r.Id == key);
                child.DeletedOn = DateTime.Now;
                child.DeletedBy = User.GetUserId();
                await _dbContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in delete child");
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("update/{id}")]
        public async Task<ActionResult<Child>> Update(int id, [FromBody] Child updatedChild)
        {
            try
            {
                var child = await _dbContext.Children.FindAsync(id);
                if (child == null)
                {
                    return NotFound();
                }

                var previousStatus = child.Status;

                // Update properties
                child.FirstName = updatedChild.FirstName;
                child.LastName = updatedChild.LastName;
                child.DateOfBirth = updatedChild.DateOfBirth;
                child.Gender = updatedChild.Gender;
                child.PrimaryLanguage = updatedChild.PrimaryLanguage;
                child.ReasonForReferral = updatedChild.ReasonForReferral;
                child.ParentName = updatedChild.ParentName;
                child.ParentPhoneNumber = updatedChild.ParentPhoneNumber;
                child.ParentEmail = updatedChild.ParentEmail;
                child.FullAddress = updatedChild.FullAddress;
                child.ReferringPhysicianName = updatedChild.ReferringPhysicianName;
                child.PhysicianPhoneNumber = updatedChild.PhysicianPhoneNumber;
                child.PhysicianEmailAddress = updatedChild.PhysicianEmailAddress;
                child.ReferralMethod = updatedChild.ReferralMethod;
                child.ReferringPhysicianId = updatedChild.ReferringPhysicianId;
                child.ProgramId = updatedChild.ProgramId;
                child.ProviderSoftChildId = updatedChild.ProviderSoftChildId;
                child.Status = updatedChild.Status ?? child.Status;

                if (child.Status != previousStatus)
                    child.StatusLastUpdated = DateTime.Now;

                await _dbContext.SaveChangesAsync();
                return Ok(child);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in updating child");
                throw;
            }
        }

        public record ChildUpdateDto(
            string? FirstName,
            string? LastName,
            DateTime? DateOfBirth,
            string? Gender,
            string? PrimaryLanguage,
            string? ReasonForReferral,
            string? ParentName,
            string? ParentPhoneNumber,
            string? ParentEmail,
            string? FullAddress,
            string? ReferringPhysicianName,
            string? PhysicianPhoneNumber,
            string? PhysicianEmailAddress,
            string? ReferralMethod,
            int? ReferringPhysicianId,
            int? ProgramId,
            int? ProviderSoftChildId,
            string Status);

        [HttpPut("{childId}")]
        public async Task<IActionResult> Update(int childId, [FromBody] ChildUpdateDto updateDto)
        {
            try
            {
                var child = await _dbContext.Children.FindAsync(childId);
                if (child == null)
                {
                    return NotFound();
                }

                var previousStatus = child.Status;

                // Update properties
                child.FirstName = updateDto.FirstName;
                child.LastName = updateDto.LastName;
                child.DateOfBirth = updateDto.DateOfBirth;
                child.Gender = updateDto.Gender;
                child.PrimaryLanguage = updateDto.PrimaryLanguage;
                child.ReasonForReferral = updateDto.ReasonForReferral;
                child.ParentName = updateDto.ParentName;
                child.ParentPhoneNumber = updateDto.ParentPhoneNumber;
                child.ParentEmail = updateDto.ParentEmail;
                child.FullAddress = updateDto.FullAddress;
                child.ReferringPhysicianName = updateDto.ReferringPhysicianName;
                child.PhysicianPhoneNumber = updateDto.PhysicianPhoneNumber;
                child.PhysicianEmailAddress = updateDto.PhysicianEmailAddress;
                child.ReferralMethod = updateDto.ReferralMethod;
                child.ReferringPhysicianId = updateDto.ReferringPhysicianId;
                child.ProgramId = updateDto.ProgramId;
                child.ProviderSoftChildId = updateDto.ProviderSoftChildId;
                child.Status = updateDto.Status;

                if (child.Status != previousStatus)
                    child.StatusLastUpdated = DateTime.Now;


                await _dbContext.SaveChangesAsync();
                return Ok(child);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error in updating child");
                throw;
            }
        }

        public class MapEiNumberToChildRequest
        {
            public string EiNumber { get; set; } = string.Empty;
            public int ChildId { get; set; }
        }

        [HttpPost("map-ei-number")]
        public async Task<IActionResult> MapEiNumberToChild([FromBody] MapEiNumberToChildRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.EiNumber))
                {
                    return BadRequest("EI Number cannot be empty");
                }

                // Find the child
                var child = await _dbContext.Children.FindAsync(request.ChildId);
                if (child == null)
                {
                    return NotFound($"Child with ID {request.ChildId} not found");
                }

                // Update the child's ProgramId with the EI Number
                child.ProgramId = int.TryParse(request.EiNumber, out int programId) ? programId : null;

                await _dbContext.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = $"Successfully mapped EI Number {request.EiNumber} to Child {child.FirstName} {child.LastName}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error mapping EI Number to Child");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}