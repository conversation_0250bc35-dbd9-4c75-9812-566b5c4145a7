import React, { useState } from "react";
import DataGrid, {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Editing,
  Toolbar,
  Item,
  Button as DgButton,
} from "devextreme-react/data-grid";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import dataGridExport from "@/app/Utils/dataGridExport";

const serviceUrl = urlHelpers.getAbsoluteURL("api/scr");

const ScrTable: React.FC = () => {
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const [focusedPackingZoneRow, setFocusedPackingZoneRow] = useState();
  const [sidePanelVisible, setSidePanelVisible] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState(null);

  const loadPackingZoneDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        insertUrl: `${serviceUrl}/Create`,
        updateUrl: `${serviceUrl}/Update`,
        deleteUrl: `${serviceUrl}/Delete`,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
      }),
    );
  };

  return (
    <DataGrid
      remoteOperations
      dataSource={remoteDataSource}
      onExporting={dataGridExport}
      height="100%"
      width="100%"
      allowColumnReordering={true}
      allowColumnResizing={true}
      columnAutoWidth={true}
      showBorders={true}
      columnResizingMode={"widget"}
      showColumnLines={false}
      rowAlternationEnabled
      focusedRowEnabled
      autoNavigateToFocusedRow
    >
      <Toolbar>
        <Item name="groupPanel" locateInMenu="auto" location="before" />
        <Item name="addRowButton" locateInMenu="auto" location="after" />
        <Item name="exportButton" locateInMenu="auto" location="after" />
        <Item name="applyFilterButton" locateInMenu="auto" location="after" />
        <Item name="revertButton" locateInMenu="auto" location="after" />
        <Item name="saveButton" locateInMenu="auto" location="after" />
        <Item name="columnChooserButton" locateInMenu="auto" location="after" />
        <Item name="searchPanel" locateInMenu="auto" location="after" />
      </Toolbar>
      <Editing mode="row" allowDeleting allowUpdating allowAdding />
      <RemoteOperations groupPaging={true} />
      <ColumnFixing enabled={true} />
      <SearchPanel visible width={250} />
      <HeaderFilter visible allowSearch />
      <ColumnChooser enabled />
      <Sorting mode="multiple" />
      <ColumnFixing />
      <Paging defaultPageSize={40} />
      <Pager showPageSizeSelector />
      <FilterPanel visible />
      <FilterBuilderPopup />
      <Export enabled={true} />
    </DataGrid>
  );
};

export default ScrTable;
