﻿using System.Net;

namespace StarEIP.Middleware
{
    public class ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
    {
        private readonly RequestDelegate next = next;
        private readonly ILogger<ExceptionHandlingMiddleware> logger = logger;

        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                logger.LogDebug("Entering {Method} {Path}", httpContext.Request.Method, httpContext.Request.Path);
                await next(httpContext);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unhandled error in {RequestMethod} {RequestPath}", httpContext.Request.Method, httpContext.Request.Path);
                httpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                await httpContext.Response.WriteAsJsonAsync(new
                {
                    Message = "Something went wrong. Please try again later."
                });
            }
        }
    }
}
