﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StarEIP.Models
{
    public class EmailTemplate
    {
        [Key]
        public required string Name { get; set; }
        public required string Subject { get; set; }
        [Column(TypeName = "nvarchar(max)")]
        public required string Body { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
