﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AddEntityTypeAndIdToNotes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "EntityId",
                table: "Notes",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityType",
                table: "Notes",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            // Update existing records to set EntityType = 'child' and EntityId = ChildId
            migrationBuilder.Sql(@"
                UPDATE Notes
                SET EntityType = 'child', EntityId = ChildId
                WHERE ChildId IS NOT NULL AND EntityType IS NULL
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EntityId",
                table: "Notes");

            migrationBuilder.DropColumn(
                name: "EntityType",
                table: "Notes");
        }
    }
}
