import React, { useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import {
  List,
  ListItem,
  ListItemText,
  Typography,
  IconButton,
  Button,
  Dialog,
  DialogContent,
  Stack,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import HouseholdMemberForm from "./HouseholdMemberForm";
import { useScrStore } from "./[id]/ScrStore";
import { Household } from "../../../../types/Household";

const HouseholdMembersList = () => {
  const [open, setOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const households = useScrStore((state) => state.households);
  const addHousehold = useScrStore((state) => state.addHousehold);
  const editHousehold = useScrStore((state) => state.editHousehold);
  const deleteHousehold = useScrStore((state) => state.deleteHousehold);

  const [editingHousehold, setEditingHousehold] = useState<Household>(
    {} as Household,
  );

  const handleClickOpen = () => {
    setEditingHousehold({} as Household);
    setOpen(true);
  };

  const handleSave = (household: Household, remove: boolean) => {
    const id = household.id || 0;
    if (remove) {
      deleteHousehold(id);
    } else if (id) {
      editHousehold(household);
    } else {
      addHousehold(household);
    }
    setOpen(false);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <List>
        {households.map((household) => (
          <ListItem
            key={household.id}
            sx={{
              border: "1px solid #ddd",
              borderRadius: "8px",
              marginBottom: "8px",
              display: "flex",
              alignItems: "flex-start",
            }}
          >
            <ListItemText
              primary={
                <Typography variant="h6" component="span">
                  {household.firstName} {household.lastName}
                </Typography>
              }
              secondary={
                <>
                  <Typography component="span" variant="caption">
                    Date of Birth:{" "}
                    {household.dateOfBirth.toLocaleDateString("en-US")}
                  </Typography>
                  <br />
                  <Typography component="span" variant="caption">
                    Sex: {household.gender}
                  </Typography>
                  <br />
                  <Typography component="span" variant="caption">
                    Relationship: {household.relationship}
                  </Typography>
                </>
              }
            />
            <IconButton
              edge="end"
              aria-label="edit"
              sx={{ alignSelf: "flex-start", marginTop: "8px" }}
            >
              <EditIcon />
            </IconButton>
          </ListItem>
        ))}
      </List>
      <Button variant="text" color="primary" onClick={handleClickOpen}>
        Add Household Member
      </Button>

      <Dialog
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            margin: isMobile ? 0 : undefined,
            padding: isMobile ? 0 : undefined,
            width: isMobile ? "100%" : undefined,
          },
        }}
      >
        <DialogContent>
          <Stack direction="column" gap={2}>
            <Typography variant="h3" component="span">
              Add Household Member
            </Typography>
            <HouseholdMemberForm
              onSave={handleSave}
              onClose={handleClose}
              household={editingHousehold}
              isPersonal={false}
            />
          </Stack>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HouseholdMembersList;
