using System;
using CsvHelper.Configuration.Attributes;

namespace StarEIP.Models.Import
{
    public class ChildDetailReportCsv
    {
        [Name("EI Child ID")]
        public string EiChildId { get; set; }

        [Name("Child Last Name")]
        public string ChildLastName { get; set; }

        [Name("Child First Name")]
        public string ChildFirstName { get; set; }

        [Name("DOB")]
        public DateTime? Dob { get; set; }

        [Name("Address")]
        public string Address { get; set; }

        [Name("City")]
        public string City { get; set; }

        [Name("State")]
        public string State { get; set; }

        [Name("Zip")]
        public string Zip { get; set; }

        [Name("Gender")]
        public string Gender { get; set; }

        [Name("Ethnicity")]
        public string Ethnicity { get; set; }

        [Name("Race")]
        public string Race { get; set; }

        [Name("Child Age")]
        public string ChildAge { get; set; }

        [Name("Primary Language")]
        public string PrimaryLanguage { get; set; }

        [Name("County Of Residence")]
        public string CountyOfResidence { get; set; }

        [Name("Address County")]
        public string AddressCounty { get; set; }

        [Name("Referral Date")]
        public DateTime? ReferralDate { get; set; }

        [Name("At-Risk Referral Date")]
        public DateTime? AtRiskReferralDate { get; set; }

        [Name("Referral At Risk Status")]
        public string ReferralAtRiskStatus { get; set; }

        [Name("Referral Type")]
        public string ReferralType { get; set; }

        [Name("Referral Method")]
        public string ReferralMethod { get; set; }

        [Name("Referral Reason")]
        public string ReferralReason { get; set; }

        [Name("Referral Source Type")]
        public string ReferralSourceType { get; set; }

        [Name("Eligibility Status")]
        public string EligibilityStatus { get; set; }

        [Name("Guardian Last Name")]
        public string GuardianLastName { get; set; }

        [Name("Guardian First Name")]
        public string GuardianFirstName { get; set; }

        [Name("Guardian Phone")]
        public string GuardianPhone { get; set; }

        [Name("Coordinator Last Name")]
        public string CoordinatorLastName { get; set; }

        [Name("Coordinator First Name")]
        public string CoordinatorFirstName { get; set; }

        [Name("Coordinator Phone")]
        public string CoordinatorPhone { get; set; }

        [Name("Coordinator Company")]
        public string CoordinatorCompany { get; set; }

        [Name("EIOD Last Name")]
        public string EiodLastName { get; set; }

        [Name("EIOD First Name")]
        public string EiodFirstName { get; set; }

        [Name("Current IFSP Start Date")]
        public DateTime? CurrentIfspStartDate { get; set; }

        [Name("Current IFSP End Date")]
        public DateTime? CurrentIfspEndDate { get; set; }

        [Name("Current IFSP Status")]
        public string CurrentIfspStatus { get; set; }

        [Name("Current IFSP type")]
        public string CurrentIfspType { get; set; }
    }
}
