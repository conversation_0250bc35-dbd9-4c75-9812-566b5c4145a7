﻿using Azure.Storage.Blobs;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.Web.WebDocumentViewer;
using DevExpress.XtraReports.Web.WebDocumentViewer.DataContracts;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using System.IO;

namespace StarEIP.Services
{
    public class CustomDocumentOperationService : DocumentOperationService
    {
        private readonly DocumentStorageService documentStorageService;

        public CustomDocumentOperationService(DocumentStorageService documentStorageService)
        {
            this.documentStorageService = documentStorageService;
        }

        public override bool CanPerformOperation(DocumentOperationRequest request)
        {
            return true;
        }

        public override async Task<DocumentOperationResponse> PerformOperationAsync(DocumentOperationRequest request, PrintingSystemBase initialPrintingSystem, PrintingSystemBase printingSystemWithEditingFields)
        {
            using MemoryStream pdfStream = new();
            printingSystemWithEditingFields.ExportToPdf(pdfStream);

            pdfStream.Seek(0, SeekOrigin.Begin);
            var customData = Newtonsoft.Json.JsonConvert.DeserializeObject<ReportParameters>(request.CustomData);
            if (customData?.ChildId.HasValue == true)
            {
                await documentStorageService.SaveDocument(customData.ChildId.Value, pdfStream, "ReferralForm.pdf", "pdf");
                return new DocumentOperationResponse
                {
                    Succeeded = true,
                    Message = "The edited report was successfully saved to Azure."
                };
            }
            else
            {
                return new DocumentOperationResponse
                {
                    Succeeded = false,
                    Message = "Child Id not set."
                };
            }
        }

        public class ReportParameters
        {
            public int? ChildId { get; set; }
            public int? AuthId { get; set; }
            public int? ProviderId { get; set; }
        }
    }
}
