﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class AuthUps : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserAssigned",
                table: "Authorization");

            migrationBuilder.RenameColumn(
                name: "UnitsApproved",
                table: "Authorization",
                newName: "UserId");

            migrationBuilder.AddColumn<int>(
                name: "ProgramId",
                table: "Child",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ProviderSoftChildId",
                table: "Child",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "StartDate",
                table: "Authorization",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<DateTime>(
                name: "EndDate",
                table: "Authorization",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<string>(
                name: "AuthType",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "AuthNumber",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AddColumn<int>(
                name: "Units",
                table: "Authorization",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Authorization_UserId",
                table: "Authorization",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Authorization_AspNetUsers_UserId",
                table: "Authorization",
                column: "UserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Authorization_AspNetUsers_UserId",
                table: "Authorization");

            migrationBuilder.DropIndex(
                name: "IX_Authorization_UserId",
                table: "Authorization");

            migrationBuilder.DropColumn(
                name: "ProgramId",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "ProviderSoftChildId",
                table: "Child");

            migrationBuilder.DropColumn(
                name: "Units",
                table: "Authorization");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "Authorization",
                newName: "UnitsApproved");

            migrationBuilder.AlterColumn<DateTime>(
                name: "StartDate",
                table: "Authorization",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "EndDate",
                table: "Authorization",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AuthType",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AuthNumber",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserAssigned",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");
        }
    }
}
