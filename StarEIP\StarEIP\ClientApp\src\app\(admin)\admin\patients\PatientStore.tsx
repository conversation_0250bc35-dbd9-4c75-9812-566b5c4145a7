import { create } from "zustand";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import { useRouter } from "next/navigation";
import { ChildDetailsDtoSchema } from "@/api/types";

export interface ChildrenStore {
  remoteDataSource: CustomStore | null;
  setRemoteDataSource: (dataSource: CustomStore) => void;
  getRemoteDataSource: () => CustomStore | null;
  dataGridRef: any;
  setDataGridRef: (ref: any) => void;
  selectedChild: ChildDetailsDtoSchema | null;
  setSelectedChild: (child: ChildDetailsDtoSchema) => void;
}

export const usePatientStore = create<ChildrenStore>((set, get) => {
  const setRemoteDataSource = (dataSource: CustomStore) => {
    set({ remoteDataSource: dataSource });
  };

  const getRemoteDataSource = () => {
    return get().remoteDataSource;
  };

  const setDataGridRef = (ref: any) => {
    set({ dataGridRef: ref });
  };

  const selectChild = (child: ChildDetailsDtoSchema) => {
    set({ selectedChild: child });
  };

  return {
    remoteDataSource: null,
    setRemoteDataSource,
    getRemoteDataSource,
    dataGridRef: null,
    setDataGridRef,
    selectedChild: null,
    setSelectedChild: selectChild,
  };
});
