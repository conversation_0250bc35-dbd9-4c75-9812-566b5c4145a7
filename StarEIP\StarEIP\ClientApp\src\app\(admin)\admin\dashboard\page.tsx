"use client";
import { Button, Container, List, Title } from "@mantine/core";
import React from "react";

const Dashboard: React.FC = () => {
  return (
    <Container size="md">
      <div>
        <h1>Coming Soon</h1>
        <p>This dashboard is under construction. Check back later!</p>
        <Title order={3}>Referral</Title>

        <List>
          <List.Item>Add new Authorization</List.Item>
          <List.Item>Add Auth grid/list</List.Item>
          <List.Item>
            Ability to generate and save reports on child/auth
          </List.Item>
          <List.Item>
            Ability to send (and log) attachment via Fax, Email, Mail.{" "}
          </List.Item>

          <List.Item>We need the option to add/edit children.</List.Item>
          <List.Item>
            We need the option to add/edit children dr referral by.
          </List.Item>
          <List.Item>failover for child submit</List.Item>
          <List.Item>
            need to setup logging (full payload logging) & IP address JWT.
          </List.Item>
          <List.Item>setup ms clarity</List.Item>
          <List.Item>reset button not working</List.Item>
        </List>

        <Title order={3}>Admin</Title>
        <List>
          <List.Item>Admin Dashboard</List.Item>
          <List.Item>Admin User Management</List.Item>
          <List.Item>Admin Referral Management</List.Item>
        </List>
      </div>
    </Container>
  );
};

export default Dashboard;
