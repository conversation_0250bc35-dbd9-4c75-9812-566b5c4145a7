﻿using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using StarEIP.Models;

namespace StarEIP.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class StaffController : ControllerBase
    {
        private readonly StarEipDbContext dbContext;
        private readonly ILogger<StaffController> logger;

        public StaffController(StarEipDbContext dbContext, ILogger<StaffController> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
        }

        [HttpGet()]
        public async Task<IActionResult> Get()
        {
            try
            {
                var openOrders = dbContext.Staff;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                dbContext.ChangeTracker.LazyLoadingEnabled = true;
                dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = [nameof(Staff.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(openOrders, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get staff");
                throw;
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreatePackingZone([FromForm] int key, [FromForm] string values)
        {
            var staff = new Staff
            {
                Email = "",
                FirstName = "",
                LastName = ""
            };
            JsonConvert.PopulateObject(values, staff);
            dbContext.Staff.Add(staff);
            await dbContext.SaveChangesAsync();
            return Ok(staff);
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdatePackingZone([FromForm] int key, [FromForm] string values)
        {
            var staff = await dbContext.Staff.SingleAsync(r => r.Id == key);
            JsonConvert.PopulateObject(values, staff);
            await dbContext.SaveChangesAsync();
            return Ok(staff);
        }

        [HttpDelete("Delete")]
        public async Task<IActionResult> DeletePackingZone([FromForm] int key)
        {
            logger.LogInformation("Entering DeletePackingZone");
            try
            {
                var staff = await dbContext.Staff.SingleAsync(r => r.Id == key);
                dbContext.Staff.Remove(staff);
                await dbContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in DeletePackingZone");
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("invite")]
        public async Task<IActionResult> InviteCandidate([FromBody] Staff request)
        {
            var candidate = new Staff
            {
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PhoneNumber = request.PhoneNumber
            };

            dbContext.Staff.Add(candidate);
            await dbContext.SaveChangesAsync();

            return Ok();
        }
    }
}
