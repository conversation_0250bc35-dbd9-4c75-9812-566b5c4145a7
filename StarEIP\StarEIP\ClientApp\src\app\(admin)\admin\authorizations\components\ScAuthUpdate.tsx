"use client";

import React, { useEffect, useState } from "react";
import { Stack, Select, Loader, Text, Tabs } from "@mantine/core";
import { Authorization, ScStatusType } from "../../../../../../types/Authorization";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import { showNotification } from "@/app/Utils/notificationUtils";
import { NotesSection } from "@/app/(admin)/admin/notes/NotesSection";
import { IconAdjustments, IconNotes } from "@tabler/icons-react";

interface ScAuthUpdateProps {
  authorization: Authorization | null;
  onAuthorizationUpdate?: (updatedAuthorization: Authorization) => void;
  onDataGridRefresh?: () => void;
}

const ScAuthUpdate: React.FC<ScAuthUpdateProps> = ({
  authorization,
  onAuthorizationUpdate,
  onDataGridRefresh,
}) => {
  const [scStatus, setScStatus] = useState<number | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [statusOptions, setStatusOptions] = useState<{ value: string; label: string }[]>([]);
  const [isLoadingStatusOptions, setIsLoadingStatusOptions] = useState(false);

  useEffect(() => {
    fetchStatusOptions();
  }, []);

  useEffect(() => {
    if (authorization) {
      setScStatus(authorization.scStatus || null);
    }
  }, [authorization]);

  const fetchStatusOptions = async () => {
    setIsLoadingStatusOptions(true);
    try {
      const response = await axios.get<ScStatusType[]>(
        urlHelpers.getAbsoluteURL("api/authorizations/sc-status-types")
      );

      const options = response.data.map((status) => ({
        value: status.id.toString(),
        label: status.name,
      }));

      setStatusOptions(options);
    } catch (error) {
      console.error("Error fetching status options:", error);
      showNotification("error", "Failed to load status options");
    } finally {
      setIsLoadingStatusOptions(false);
    }
  };

  const handleUpdateStatus = async (statusToUpdate?: number) => {
    const statusValue = statusToUpdate ?? scStatus;
    if (!statusValue || !authorization) return;

    setIsUpdating(true);
    try {
      await axios.post(
        urlHelpers.getAbsoluteURL(`api/authorizations/${authorization.id}/sc-status`),
        {
          scStatus: statusValue,
          followUpDate: null, // You can extend this to include followUpDate if needed
        }
      );

      const statusName = statusOptions.find((option) => option.value === statusValue.toString())?.label || "";

      showNotification("success", `SC Status updated to ${statusName}`);

      // Update the authorization with new data
      const updatedAuthorization = {
        ...authorization,
        scStatus: statusValue,
        scStatusName: statusName,
        scStatusLastUpdated: new Date(),
      };

      // Call the callback to update parent state
      if (onAuthorizationUpdate) {
        onAuthorizationUpdate(updatedAuthorization);
      }

      // Refresh the data grid if callback provided
      if (onDataGridRefresh) {
        onDataGridRefresh();
      }
    } catch (error) {
      console.error("Error updating SC status:", error);
      showNotification("error", "Failed to update SC status");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleScStatusChange = async (value: string | null) => {
    if (!authorization) return;

    const newScStatus = value ? parseInt(value) : null;
    setScStatus(newScStatus);

    if (newScStatus) {
      await handleUpdateStatus(newScStatus);
    }
  };

  if (!authorization) {
    return (
      <Stack gap="md" style={{ height: "100%", overflow: "hidden" }} p="md">
        <Text size="sm" c="dimmed" ta="center">
          Select an authorization to view details
        </Text>
      </Stack>
    );
  }

  return (
    <Tabs defaultValue="status" style={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <Tabs.List>
        <Tabs.Tab value="status" leftSection={<IconAdjustments size={16} />}>
          SC Status
        </Tabs.Tab>
        <Tabs.Tab value="notes" leftSection={<IconNotes size={16} />}>
          Notes
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel
        value="status"
        style={{
          height: "100%",
          display: "flex",
          flex: 1,
          flexGrow: 1,
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        <Stack gap="md" style={{ height: "100%", overflow: "hidden" }} p="md">
          {isLoadingStatusOptions ? (
            <Loader size="md" />
          ) : (
            <Select
              size="xs"
              label="SC Status"
              placeholder="Select status"
              value={scStatus?.toString()}
              onChange={handleScStatusChange}
              data={statusOptions}
              clearable
              disabled={isUpdating}
            />
          )}
          {authorization.scStatusLastUpdated && (
            <Text size="xs" c="dimmed">
              Last updated: {new Date(authorization.scStatusLastUpdated).toLocaleString()}
            </Text>
          )}

          {isUpdating && (
            <Text size="sm" c="dimmed">
              Updating...
            </Text>
          )}
        </Stack>
      </Tabs.Panel>

      <Tabs.Panel
        value="notes"
        style={{
          height: "100%",
          display: "flex",
          flex: 1,
          flexGrow: 1,
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        <NotesSection
          entityType="authorization"
          entityId={authorization.id}
          maxLength={500}
          enableDelete={true}
        />
      </Tabs.Panel>
    </Tabs>
  );
};

export default ScAuthUpdate;
