import React from "react";
import { Card, Text, Group } from "@mantine/core";

interface Physician {
  name: string;
  phoneNumber: string;
  emailAddress: string;
  streetAddress: string;
  city: string;
  state: string;
  postalCode: string;
  notes: string;
  facilityName: string;
  referralSourceType: string;
}

const PhysicianCard = ({ physician }: { physician: Physician }) => {
  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Group style={{ marginBottom: 5 }}>
        <Text>{physician.name}</Text>
      </Group>

      <Text size="sm" style={{ lineHeight: 1.5 }}>
        <strong>Phone Number:</strong> {physician.phoneNumber}
      </Text>
      <Text size="sm" style={{ lineHeight: 1.5 }}>
        <strong>Email Address:</strong> {physician.emailAddress}
      </Text>
      <Text size="sm" style={{ lineHeight: 1.5 }}>
        <strong>Address:</strong>{" "}
        {`${physician.streetAddress}, ${physician.city}, ${physician.state} ${physician.postalCode}`}
      </Text>
      <Text size="sm" style={{ lineHeight: 1.5 }}>
        <strong>Notes:</strong> {physician.notes}
      </Text>
      <Text size="sm" style={{ lineHeight: 1.5 }}>
        <strong>Facility Name:</strong> {physician.facilityName}
      </Text>
      <Text size="sm" style={{ lineHeight: 1.5 }}>
        <strong>Referral Source Type:</strong> {physician.referralSourceType}
      </Text>
    </Card>
  );
};

export default PhysicianCard;
