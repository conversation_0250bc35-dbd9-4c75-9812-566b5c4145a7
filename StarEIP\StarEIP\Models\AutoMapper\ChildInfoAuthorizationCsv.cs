using CsvHelper.Configuration.Attributes;

public class ChildInfoAuthorizationCsv
{
    [Name("Billing Provider")]
    public string BillingProvider { get; set; }

    [Name("EIP Provider ID")]
    public string EIPProviderID { get; set; }

    [Name("EI Child ID")]
    public string EIChildID { get; set; }

    [Name("Child First Name")]
    public string ChildFirstName { get; set; }

    [Name("Child Middle Name")]
    public string ChildMiddleName { get; set; }

    [Name("Child Last Name")]
    public string ChildLastName { get; set; }

    [Name("DOB")]
    public DateTime? DOB { get; set; }

    [Name("Child Address")]
    public string ChildAddress { get; set; }

    [Name("Child City")]
    public string ChildCity { get; set; }

    [Name("Child State")]
    public string ChildState { get; set; }

    [Name("Child Zip")]
    public string ChildZip { get; set; }

    [Name("Sex")]
    public string Sex { get; set; }

    [Name("County of Residence")]
    public string CountyOfResidence { get; set; }

    [Name("Child Status")]
    public string ChildStatus { get; set; }

    [Name("Date Aging Out")]
    public DateTime? DateAgingOut { get; set; }

    [Name("Service Coordinator First Name")]
    public string ServiceCoordinatorFirstName { get; set; }

    [Name("Service Coordinator Last Name")]
    public string ServiceCoordinatorLastName { get; set; }

    [Name("Service Coordinator Company")]
    public string ServiceCoordinatorCompany { get; set; }

    [Name("Service Coordinator Phone")]
    public string ServiceCoordinatorPhone { get; set; }

    [Name("Service Coordinator Email")]
    public string ServiceCoordinatorEmail { get; set; }

    [Name("EIO/D First Name")]
    public string EIODFirstName { get; set; }

    [Name("EIO/D Last Name")]
    public string EIODLastName { get; set; }

    [Name("EIO/D Phone")]
    public string EIODPhone { get; set; }

    [Name("EIO/D Email")]
    public string EIODEmail { get; set; }

    [Name("Program")]
    public string Program { get; set; }

    [Name("Enrollment Type")]
    public string EnrollmentType { get; set; }

    [Name("Enrollment Status")]
    public string EnrollmentStatus { get; set; }

    [Name("Authorization Number")]
    public string AuthorizationNumber { get; set; }

    [Name("Start Date")]
    public DateTime? StartDate { get; set; }

    [Name("End Date")]
    public DateTime? EndDate { get; set; }

    [Name("Location Type")]
    public string LocationType { get; set; }

    [Name("Suspended Start Date")]
    public DateTime? SuspendedStartDate { get; set; }

    [Name("Suspended End Date")]
    public DateTime? SuspendedEndDate { get; set; }

    [Name("Length")]
    public string Length { get; set; }

    [Name("Frequency")]
    public string Frequency { get; set; }

    [Name("Frequency Unit")]
    public string FrequencyUnit { get; set; }

    [Name("Visits Per Day")]
    public string VisitsPerDay { get; set; }

    [Name("IFSP ID")]
    public string IFSPID { get; set; }

    [Name("IFSP Signed Date")]
    public DateTime? IFSPSignedDate { get; set; }

    [Name("IFSP Type")]
    public string IFSPType { get; set; }

    [Name("IFSP Status")]
    public string IFSPStatus { get; set; }

    [Name("IFSP Start Date")]
    public DateTime? IFSPStartDate { get; set; }

    [Name("IFSP End Date")]
    public DateTime? IFSPEndDate { get; set; }

    [Name("Exit Date")]
    public DateTime? ExitDate { get; set; }

    [Name("Therapist First Name")]
    public string TherapistFirstName { get; set; }

    [Name("Therapist Last Name")]
    public string TherapistLastName { get; set; }

    [Name("Therapist NPI")]
    public string TherapistNPI { get; set; }

    [Name("Referring Provider First Name")]
    public string ReferringProviderFirstName { get; set; }

    [Name("Referring Provider Last Name")]
    public string ReferringProviderLastName { get; set; }

    [Name("Referring Provider NPI")]
    public string ReferringProviderNPI { get; set; }

    [Name("Last Session Date")]
    public DateTime? LastSessionDate { get; set; }

    [Name("Total Sessions Authorized")]
    public int? TotalSessionsAuthorized { get; set; }

    [Name("Total Sessions Used")]
    public int? TotalSessionsUsed { get; set; }

    [Name("Number of Make-Up Session/Units Authorized")]
    public int? MakeUpSessionsAuthorized { get; set; }

    [Name("Total Make-Up Session/Units Used")]
    public int? MakeUpSessionsUsed { get; set; }

    [Name("Make-Up Units Remaining")]
    public int? MakeUpUnitsRemaining { get; set; }

    [Name("Total CoVisit Units")]
    public int? TotalCoVisitUnits { get; set; }

    [Name("Co-Visit Units Used")]
    public int? CoVisitUnitsUsed { get; set; }

    [Name("Co-Visit Units Remaining")]
    public int? CoVisitUnitsRemaining { get; set; }

    [Name("Number of Remaining Units/Sessions")]
    public int? RemainingUnitsOrSessions { get; set; }
}
