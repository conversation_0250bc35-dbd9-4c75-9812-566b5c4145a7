import { Authorization } from "../../../../../../types/Authorization";
import React, { useState } from "react";
import { ActionIcon, Group, Select, Title } from "@mantine/core";
import { IconCheck, IconEdit } from "@tabler/icons-react";
import { useAuthorizationStore } from "@/app/(admin)/admin/authorizations/AuthorizationStore";
import { notifications } from "@mantine/notifications";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";

const serviceUrl = urlHelpers.getAbsoluteURL("api/authorizations");

interface AuthorizationCardHeader {
  authorization: Authorization;
  statuses: { id: string; name: string }[];
  onEditClick?: () => void;
  showEdit: boolean;
  onStatusChange?: (updatedAuthorization: Authorization) => void;
}

const AuthorizationCardHeader: React.FC<AuthorizationCardHeader> = ({
  authorization,
  statuses,
  onEditClick,
  showEdit = true,
  onStatusChange,
}) => {
  const { dataGridRef } = useAuthorizationStore();
  const [currentStatus, setCurrentStatus] = useState(authorization.statusId);

  const updateStatus = async (newStatus: string) => {
    const notificationId = notifications.show({
      loading: true,
      title: "Updating status",
      message: `Updating status to ${newStatus}`,
      autoClose: false,
      withCloseButton: false,
    });

    try {
      await axios.post(`${serviceUrl}/${authorization.id}/status`, {
        statusId: newStatus,
      });

      setCurrentStatus(newStatus);

      const updatedAuthorization = { ...authorization, statusId: newStatus };
      if (onStatusChange) {
        onStatusChange(updatedAuthorization);
      }

      notifications.update({
        id: notificationId,
        title: "Status updated",
        message: `Status updated to ${newStatus}`,
        icon: <IconCheck size={18} />,
        loading: false,
        autoClose: 2000,
      });

      const grid = dataGridRef?.current?.instance?.();
      if (grid) {
        console.log("grid have value ");
        grid.getDataSource().reload();
      }

      // const grid = dataGridRef?.current.instance();
      // if (grid) {
      //     const index = grid.getRowIndexByKey(authorization.id);
      //     grid.repaint();
      //     grid.repaintRows([index]);
      // }
    } catch (e) {
      notifications.update({
        id: notificationId,
        title: "Error",
        message: "Failed to update status. Please try again later.",
        icon: <IconCheck size={18} />,
        loading: false,
        autoClose: 2000,
      });
    }
  };

  return (
    <Group justify="space-between" px="md">
      <Title order={4}>{authorization.authNumber}</Title>
      <Group>
        {showEdit && (
          <ActionIcon variant="default" size="xs" onClick={onEditClick}>
            <IconEdit size={16} />
          </ActionIcon>
        )}
        <Select
          value={currentStatus}
          onChange={(value) => value && updateStatus(value)}
          data={statuses.map((s) => ({ value: s.id, label: s.name }))}
          size="xs"
          radius="xl"
        />
      </Group>
    </Group>
  );
};

export default AuthorizationCardHeader;
