﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class UpdatedBaseClass : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "SharedAttachmentLogs");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "ChildAttachments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AuthLogs");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "SharedAttachmentLogs",
                newName: "UpdatedOn");

            migrationBuilder.RenameColumn(
                name: "DeletedAt",
                table: "SharedAttachmentLogs",
                newName: "DeletedOn");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "SharedAttachmentLogs",
                newName: "CreatedOn");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "Contacts",
                newName: "UpdatedOn");

            migrationBuilder.RenameColumn(
                name: "DeletedAt",
                table: "Contacts",
                newName: "DeletedOn");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "Contacts",
                newName: "CreatedOn");

            migrationBuilder.RenameColumn(
                name: "UploadedOn",
                table: "ChildAttachments",
                newName: "UpdatedOn");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "ChildAttachments",
                newName: "CreatedOn");

            migrationBuilder.RenameColumn(
                name: "DeletedAt",
                table: "ChildAttachments",
                newName: "DeletedOn");

            migrationBuilder.RenameColumn(
                name: "UpdatedAt",
                table: "AuthLogs",
                newName: "UpdatedOn");

            migrationBuilder.RenameColumn(
                name: "DeletedAt",
                table: "AuthLogs",
                newName: "DeletedOn");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "AuthLogs",
                newName: "CreatedOn");

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "SharedAttachmentLogs",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "SharedAttachmentLogs",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "SharedAttachmentLogs",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PhoneNumber",
                table: "Contacts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "FaxNumber",
                table: "Contacts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "Contacts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255);

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "ChildAttachments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "ChildAttachments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "ChildAttachments",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CreatedBy",
                table: "AuthLogs",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "DeletedBy",
                table: "AuthLogs",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "UpdatedBy",
                table: "AuthLogs",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "SharedAttachmentLogs");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "SharedAttachmentLogs");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "SharedAttachmentLogs");

            migrationBuilder.DropColumn(
                name: "ShowOnChildDetails",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "ChildAttachments");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "ChildAttachments");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "ChildAttachments");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "AuthLogs");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "AuthLogs");

            migrationBuilder.DropColumn(
                name: "UpdatedBy",
                table: "AuthLogs");

            migrationBuilder.RenameColumn(
                name: "UpdatedOn",
                table: "SharedAttachmentLogs",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "DeletedOn",
                table: "SharedAttachmentLogs",
                newName: "DeletedAt");

            migrationBuilder.RenameColumn(
                name: "CreatedOn",
                table: "SharedAttachmentLogs",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "UpdatedOn",
                table: "Contacts",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "DeletedOn",
                table: "Contacts",
                newName: "DeletedAt");

            migrationBuilder.RenameColumn(
                name: "CreatedOn",
                table: "Contacts",
                newName: "CreatedAt");

            migrationBuilder.RenameColumn(
                name: "UpdatedOn",
                table: "ChildAttachments",
                newName: "UploadedOn");

            migrationBuilder.RenameColumn(
                name: "DeletedOn",
                table: "ChildAttachments",
                newName: "DeletedAt");

            migrationBuilder.RenameColumn(
                name: "CreatedOn",
                table: "ChildAttachments",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "UpdatedOn",
                table: "AuthLogs",
                newName: "UpdatedAt");

            migrationBuilder.RenameColumn(
                name: "DeletedOn",
                table: "AuthLogs",
                newName: "DeletedAt");

            migrationBuilder.RenameColumn(
                name: "CreatedOn",
                table: "AuthLogs",
                newName: "CreatedAt");

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "SharedAttachmentLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "PhoneNumber",
                table: "Contacts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FaxNumber",
                table: "Contacts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "Contacts",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "Contacts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "ChildAttachments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AuthLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
