@inherits LayoutComponentBase

<div class="page">
    <div class="sidebar">
        <!-- Sidebar content -->
    </div>
    <div class="main">
        <div class="top-row px-4">
            <a href="https://docs.microsoft.com/aspnet/" target="_blank">About</a>
        </div>

        <div class="content px-4">
            @Body
        </div>
    </div>
</div>

<style>
    .page {
        display: flex;
        flex-direction: row;
    }

    .sidebar {
        width: 250px;
        background-color: #f8f9fa;
    }

    .main {
        flex: 1;
        padding: 1rem;
    }

    .top-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .content {
        margin-top: 1rem;
    }
</style>