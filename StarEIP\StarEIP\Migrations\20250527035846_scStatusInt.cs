﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StarEIP.Migrations
{
    /// <inheritdoc />
    public partial class scStatusInt : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "ScStatus",
                table: "Authorization",
                type: "int",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.CreateTable(
                name: "ScStatusType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    SortOrder = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScStatusType", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Authorization_ScStatus",
                table: "Authorization",
                column: "ScStatus");

            migrationBuilder.AddForeignKey(
                name: "FK_Authorization_ScStatusType_ScStatus",
                table: "Authorization",
                column: "ScStatus",
                principalTable: "ScStatusType",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Authorization_ScStatusType_ScStatus",
                table: "Authorization");

            migrationBuilder.DropTable(
                name: "ScStatusType");

            migrationBuilder.DropIndex(
                name: "IX_Authorization_ScStatus",
                table: "Authorization");

            migrationBuilder.AlterColumn<string>(
                name: "ScStatus",
                table: "Authorization",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);
        }
    }
}
