﻿namespace StarEIP.Models.App.PhoneApi
{

    public class MessageResponse
    {
        public List<MessageItem> items { get; set; }
        public int total { get; set; }
        public int limit { get; set; }
        public int offset { get; set; }
    }

    public class MessageItem
    {
        public long id { get; set; }
        public string message_id { get; set; }
        public string text { get; set; }
        public string conversation_id { get; set; }
        public string type { get; set; }
        public string tag { get; set; }
        public string from { get; set; }
        public long? read_at { get; set; }
        public long created_at { get; set; }
        public long updated_at { get; set; }
        public string direction { get; set; }
        public List<MessageTo> to { get; set; }
        public int extension_id { get; set; } 
    }

    public class MessageTo
    {
        public string number { get; set; }
        public string voip_contact_id { get; set; }
        public long sent_at { get; set; }
        public long? delivered_at { get; set; }
        public string delivery_status { get; set; }
        public string name { get; set; }
    }

}
