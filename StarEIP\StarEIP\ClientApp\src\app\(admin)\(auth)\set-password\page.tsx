"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";
import {
  <PERSON><PERSON>,
  Button,
  Container,
  Paper,
  PasswordInput,
  Popover,
  Progress,
  Text,
  Title,
} from "@mantine/core";
import { IconAlertCircle, IconCheck, IconX } from "@tabler/icons-react";
import { useForm } from "@mantine/form";

const serviceUrl = urlHelpers.getAbsoluteURL("/api/auth");

const PasswordRequirement = ({
  meets,
  label,
}: {
  meets: boolean;
  label: string;
}) => {
  return (
    <Text c={meets ? "brand" : "red"} mt={5} size="sm">
      {meets ? <IconCheck size="0.9rem" /> : <IconX size="0.9rem" />}{" "}
      <span>{label}</span>
    </Text>
  );
};

const requirements = [
  { re: /[0-9]/, label: "Includes number" },
  { re: /[a-z]/, label: "Includes lowercase letter" },
  { re: /[A-Z]/, label: "Includes uppercase letter" },
  { re: /[$&+,:;=?@#|'<>.^*()%!-]/, label: "Includes special character" },
];

const getStrength = (password: string) => {
  let multiplier = password.length > 7 ? 0 : 1;

  requirements.forEach((req) => {
    if (!req.re.test(password)) {
      multiplier++;
    }
  });

  return Math.max(100 - (100 / (requirements.length + 1)) * multiplier, 0);
};

const SetPasswordPage = () => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [popoverOpened, setPopoverOpen] = useState(false);

  const searchParams = useSearchParams();
  const router = useRouter();

  const form = useForm({
    initialValues: {
      password: "",
      confirmPassword: "",
    },
    validate: {
      password: (value) => {
        if (value.length < 8)
          return "Password must be at least 8 characters long.";
        if (!/[a-z]/.test(value))
          return "Password must contain at least one lowercase letter.";
        if (!/[A-Z]/.test(value))
          return "Password must contain at least one uppercase letter.";
        if (!/[$&+,:;=?@#|'<>.^*()%!-]/.test(value))
          return "Password must contain at least one special character.";
        return null;
      },
      confirmPassword: (value, values) =>
        value !== values.password && "Passwords do not match.",
    },
  });

  const handleSubmit = async (values: {
    password: string;
    confirmPassword: string;
  }) => {
    setErrorMessage("");
    setSuccessMessage("");
    setLoading(true);

    const token = searchParams.get("token");
    const email = searchParams.get("email");

    if (!token || !email) {
      setErrorMessage("Invalid request. Missing token or email.");
      setLoading(false);
      return;
    }

    try {
      const payload = {
        emailAddress: email,
        password: values.password,
        token: token,
      };

      const response = await axios.post(`${serviceUrl}/set-password`, payload);

      if (response.status === 200) {
        setSuccessMessage("Password set successfully! Redirecting to login...");
        setTimeout(() => {
          router.push("/login");
        }, 3000);
      } else {
        setErrorMessage("Failed to set password. Please try again later.");
      }
    } catch (error) {
      setErrorMessage(
        "An error occurred while trying to set the password. Please try again later.",
      );
    } finally {
      setLoading(false);
    }
  };

  const strength = getStrength(form.values.password);
  const checks = requirements.map((req, index) => (
    <PasswordRequirement
      key={index}
      meets={req.re.test(form.values.password)}
      label={req.label}
    />
  ));

  const bars = Array(4)
    .fill(0)
    .map((_, index) => (
      <Progress
        key={index}
        value={
          form.values.password.length > 0 && index === 0
            ? 100
            : strength >= ((index + 1) / 4) * 100
              ? 100
              : 0
        }
        color={strength > 80 ? "teal" : strength > 50 ? "yellow" : "red"}
      />
    ));

  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        Set New Password
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        Enter a new password that meets the requirements.
      </Text>
      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {errorMessage && (
          <Alert
            icon={<IconAlertCircle size="1.1rem" />}
            title="Error"
            color="red"
            mb="xl"
          >
            {errorMessage}
          </Alert>
        )}
        {successMessage && (
          <Alert
            icon={<IconCheck size="1.1rem" />}
            title="Success"
            color="green"
          >
            {successMessage}
          </Alert>
        )}
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Popover
            opened={popoverOpened}
            position="bottom"
            width="target"
            transitionProps={{ transition: "pop" }}
          >
            <Popover.Target>
              <div
                onFocusCapture={() => setPopoverOpen(true)}
                onBlurCapture={() => setPopoverOpen(false)}
              >
                <PasswordInput
                  label="Password"
                  placeholder="Enter your password"
                  required
                  mt="md"
                  {...form.getInputProps("password")}
                />
              </div>
            </Popover.Target>
            <Popover.Dropdown>
              <Progress
                color={
                  strength > 80 ? "teal" : strength > 50 ? "yellow" : "red"
                }
                value={strength}
                size={5}
                mb="xs"
              />
              <PasswordRequirement
                meets={form.values.password.length > 7}
                label="Includes at least 8 characters"
              />
              {checks}
            </Popover.Dropdown>
          </Popover>

          <PasswordInput
            label="Confirm Password"
            placeholder="Confirm your password"
            required
            mt="md"
            {...form.getInputProps("confirmPassword")}
          />

          <Button
            fullWidth
            mt="xl"
            type="submit"
            disabled={loading}
            loading={loading}
          >
            Set Password
          </Button>
        </form>
      </Paper>
    </Container>
  );
};

export default SetPasswordPage;
