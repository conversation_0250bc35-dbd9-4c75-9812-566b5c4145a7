﻿using CsvHelper.Configuration.Attributes;

namespace StarEIP.Models.AutoMapper
{
    public class ReconciliationEntryCsv
    {
        [Name("Child Name")]
        public string ChildName { get; set; }

        [Name("EI#")]
        public string EiNumber { get; set; }

        public string Provider { get; set; }

        [Name("Session Date")]
        public DateTime SessionDate { get; set; }

        [Name("Claim Created")]
        public DateTime ClaimCreated { get; set; }

        public string Type { get; set; }

        [Name("Sub Type")]
        public string SubType { get; set; }

        [Name("Bill Amount")]
        [TypeConverter(typeof(CurrencyConverter))]
        public decimal BillAmount { get; set; }

        [Name("Remitt Status")]
        public string RemittStatus { get; set; }

        [Name("Remitt Date")]
        public DateTime? RemittDate { get; set; }

        [Name("Pymt Amt")]
        [TypeConverter(typeof(CurrencyConverter))]
        public decimal PaymentAmount { get; set; }

        [Name("Funding Source")]
        public string FundingSource { get; set; }

        [TypeConverter(typeof(CurrencyConverter))]
        public decimal Discrepancy { get; set; }

        [Name("Adjust Reason Codes")]
        public string AdjustReasonCodes { get; set; }

        [Name("Adjust Reason Description")]
        public string AdjustReasonDescription { get; set; }

        public string Reconciled { get; set; }
    }
}