"use client";

import { useState, useRef, useEffect } from "react";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Editing,
  <PERSON><PERSON><PERSON>,
  Item,
  Button as Dg<PERSON><PERSON>on,
  Lookup,
  DataGridTypes,
} from "devextreme-react/data-grid";
import { createStore, CustomStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { useRouter } from "next/navigation";
import dataGridExport from "@/app/Utils/dataGridExport";
import But<PERSON> from "devextreme-react/button";

const serviceUrl = urlHelpers.getAbsoluteURL("api/physicians");

const referralSourceTypes = [
  { id: 0, name: "EI Provider" },
  { id: 1, name: "Hospital" },
  { id: 2, name: "Healthcare Provider" },
  { id: 3, name: "Parent/Family" },
  { id: 4, name: "Social Service Agency" },
];

interface PhysicianPageProps {
  onRowClick?: (e: DataGridTypes.RowClickEvent) => void;
}

const PhysicianPage = ({ onRowClick }: PhysicianPageProps) => {
  const [remoteDataSource, setRemoteDataSource] = useState<CustomStore>();
  const router = useRouter();
  const dataGridRef = useRef(null);

  const loadPhysiciansDataSource = async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: serviceUrl,
        updateUrl: `${serviceUrl}/update`,
        insertUrl: `${serviceUrl}/create`,
        onBeforeSend: (e, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  };

  useEffect(() => {
    loadPhysiciansDataSource();
  }, []);

  return (
    <>
      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        showColumnLines={false}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onRowClick={onRowClick}
      >
        <Toolbar>
          <Item location="before">
            <Button icon="refresh" onClick={loadPhysiciansDataSource} />
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item name="addRowButton" locateInMenu="auto" location="before" />
          {/* <Item location="before">
                        <Button icon="add" text='Add User' onClick={open} />
                    </Item> */}
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item name="applyFilterButton" locateInMenu="auto" location="after" />
          <Item name="revertButton" locateInMenu="auto" location="after" />
          <Item name="saveButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <Editing allowUpdating allowAdding mode="popup" />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <ColumnFixing />
        <Paging defaultPageSize={40} />
        <Pager showPageSizeSelector />
        <FilterPanel visible />
        <FilterBuilderPopup />
        <Export enabled={true} />

        <Column type="buttons" cssClass={"text-align-right"}>
          <DgButton name="edit" />
        </Column>
        <Column
          dataField="id"
          caption="ID"
          allowEditing={false}
          visible={false}
        />
        <Column dataField="name" caption="Name" />
        <Column dataField="phoneNumber" caption="Phone Number" />
        <Column dataField="emailAddress" caption="Email Address" />
        <Column dataField="streetAddress" caption="Street Address" />
        <Column dataField="city" caption="City" />
        <Column dataField="state" caption="State" />
        <Column dataField="postalCode" caption="Postal Code" />
        <Column dataField="notes" caption="Notes" />
        <Column dataField="facilityName" caption="Facility Name" />
        <Column dataField="referralSourceType" caption="Referral Source Type">
          <Lookup
            dataSource={referralSourceTypes}
            displayExpr="name"
            valueExpr="id"
          />
        </Column>
      </DataGrid>
    </>
  );
};

export default PhysicianPage;
