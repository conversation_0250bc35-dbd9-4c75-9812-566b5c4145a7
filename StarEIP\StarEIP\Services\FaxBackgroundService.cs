﻿
using Azure.Storage.Blobs;
using DevExpress.Pdf.Native;
using Flurl;
using Flurl.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using StarEIP.Configuration;
using StarEIP.Models;
using StarEIP.Models.App.TelebroadApi;
using System.Text.Json;

namespace StarEIP.Services
{
    public class FaxBackgroundService : BackgroundService
    {
        private readonly ILogger<FaxBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly BlobServiceClient _blobServiceClient;

        public FaxBackgroundService(
            ILogger<FaxBackgroundService> logger,
            IServiceProvider serviceProvider,
            BlobServiceClient blobServiceClient)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _blobServiceClient = blobServiceClient;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("FaxBackgroundService running at: {Time}", DateTimeOffset.Now);
                using (var scope = _serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<StarEipDbContext>();
                    var telebroadApiService = scope.ServiceProvider.GetRequiredService<TelebroadApiService>();
                    await DownloadFaxes(telebroadApiService, dbContext, stoppingToken);
                    await DownloadMissingPdf(telebroadApiService, dbContext, stoppingToken);
                }
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }

        private async Task DownloadFaxes(TelebroadApiService telebroadApiService, StarEipDbContext dbContext, CancellationToken stoppingToken)
        {
            try
            {                
                var offset = 0;
                var lastSavedFax = await dbContext.Faxes.OrderByDescending(f => f.CreatedAt).FirstOrDefaultAsync(stoppingToken);
                var lastSavedFaxTime = lastSavedFax != null ? $"gt:{new DateTimeOffset(lastSavedFax.CreatedAt).ToUnixTimeSeconds()}" : null;

                do
                {
                    var faxResponse = await telebroadApiService.GetFaxMessagesAsync(offset, 100, lastSavedFaxTime?.Replace("gt:", ""));
                    if (faxResponse.error != null)
                    {
                        throw new Exception($"Error retrieving SMS conversations: {faxResponse.error}");
                    }

                    var faxes = faxResponse.result;
                    if (faxes == null || faxes.Count == 0)
                        break;

                    foreach (var fax in faxes)
                    {
                        var faxEntity = await dbContext.Faxes.FirstOrDefaultAsync(f => f.FaxId == fax.Id, stoppingToken);
                        if (faxEntity == null)
                        {
                            faxEntity = new Fax { FaxId = fax.Id };
                            MapFax(faxEntity, fax);
                            await dbContext.Faxes.AddAsync(faxEntity, stoppingToken);
                        }
                        MapFax(faxEntity, fax);
                    }
                    await dbContext.SaveChangesAsync(stoppingToken);
                    offset += faxes.Count;
                } while (offset > 0);
            }
            catch (FlurlHttpException ex)
            {
                var rawJson = await ex.GetResponseStringAsync();
                _logger.LogError(ex, "Error occurred while checking for new faxes. {Response}", rawJson);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking for new faxes");
            }
        }

        private async Task DownloadMissingPdf(TelebroadApiService telebroadApiService, StarEipDbContext dbContext, CancellationToken stoppingToken)
        {
            var faxes = await dbContext.Faxes.Where(f => !f.PdfSaved).ToListAsync(stoppingToken);
            foreach (var fax in faxes)
            {
                try
                {
                    string direction = fax.Direction.Equals("in", StringComparison.OrdinalIgnoreCase) ? "INBOX" : "SENT";
                    var telebroadFaxDetail = await telebroadApiService.GetFaxMessageAsync(fax, direction);

                    // Decode Base64 string into a stream
                    var base64Data = telebroadFaxDetail.result.Data;
                    if (string.IsNullOrEmpty(base64Data))
                    {
                        throw new Exception($"Fax {fax.FaxId} has no data to save.");
                    }

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    using var fileStream = new MemoryStream(fileBytes);

                    var containerClient = _blobServiceClient.GetBlobContainerClient("faxes");
                    var blobClient = containerClient.GetBlobClient($"{fax.FaxId}.pdf");
                    await blobClient.UploadAsync(fileStream, overwrite: true, cancellationToken: stoppingToken);

                    fax.PdfSaved = true;
                    await dbContext.SaveChangesAsync(stoppingToken);
                }
                catch (FormatException ex)
                {
                    _logger.LogError(ex, "Invalid Base64 string for fax {FaxId}", fax.FaxId);
                }
                catch (FlurlHttpException ex)
                {
                    var rawJson = await ex.GetResponseStringAsync();
                    _logger.LogError(ex, "Error occurred while downloading pdf for fax {FaxId}. {Response}", fax.FaxId, rawJson);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while downloading pdf for fax {FaxId}", fax.FaxId);
                }
            }
        }


        private static void MapFax(Fax faxEntity, TelebroadFaxMessages fax)
        {
            faxEntity.FaxId = fax.Id;
            faxEntity.FromNumber = fax.Caller;
            faxEntity.To = fax.Called;
            faxEntity.Pages = fax.Pages;
            faxEntity.CreatedAt = DateTimeOffset.FromUnixTimeSeconds(fax.Time).DateTime;
            faxEntity.Direction = fax.Directory;
            faxEntity.FileName = fax.Name; // Store the name property from the API response
            faxEntity.DownloadUrl = fax.Name;
            faxEntity.PdfSaved = faxEntity.PdfSaved;
        }
    }
}
