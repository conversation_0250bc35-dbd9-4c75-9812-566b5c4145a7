import React from "react";
import {
  HoverCard,
  Stack,
  Text,
  Divider,
  ActionIcon,
  Group,
  rem,
  Tooltip,
} from "@mantine/core";
import {
  IconEye,
  IconPencil,
  IconCalendar,
  IconClock,
} from "@tabler/icons-react";
import Link from "next/link";
import { ChildDetailsDtoSchema } from "@/api/types";

interface PatientHoverCardProps {
  childDetails: ChildDetailsDtoSchema;
  children: React.ReactNode;
  position?: "top" | "right" | "bottom" | "left"; // Add position prop
}

const PatientHoverCard: React.FC<PatientHoverCardProps> = ({
  childDetails,
  children,
  position = "left",
}) => {
  const { id, providerSoftChildId, referralMethod, createdOn, updatedOn } =
    childDetails;

  const dateOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  };
  const formattedCreatedOn = createdOn
    ? new Date(createdOn).toLocaleString(undefined, dateOptions)
    : "N/A";
  const formattedUpdatedOn = updatedOn
    ? new Date(updatedOn).toLocaleString(undefined, dateOptions)
    : "N/A";

  const providerSoftUrl = `https://web3.providersoftllc.com/StarEIP/Infants/InfantInfo.aspx?PatientId=${providerSoftChildId}`;

  return (
    <HoverCard
      width={200}
      shadow="xl"
      position={position}
      withArrow
      openDelay={50}
    >
      <HoverCard.Target>{children}</HoverCard.Target>
      <HoverCard.Dropdown
        p="xs"
        style={{
          fontFamily: "Roboto Condensed, sans-serif",
          backgroundColor: "lightgray",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
          border: "1px solid #e9ecef",
          borderRadius: 4,
        }}
      >
        <Stack gap={1}>
          <Group gap={1} justify="space-between">
            <Tooltip label="Provider Soft" withArrow>
              <ActionIcon
                component="a"
                href={providerSoftUrl}
                target="_blank"
                rel="noopener noreferrer"
                size="sm"
                disabled={!providerSoftChildId}
              >
                <img
                  src="https://web3.providersoftllc.com/favicon.ico"
                  alt="Provider Soft"
                  style={{ width: 16, height: 16 }}
                />
              </ActionIcon>
            </Tooltip>

            <Group gap={5}>
              <Tooltip label="Open" withArrow>
                <ActionIcon
                  component={Link}
                  href={`/admin/patients/${id}`}
                  size="sm"
                >
                  <IconEye size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="Edit" withArrow>
                <ActionIcon
                  component={Link}
                  href={`/admin/patients/${id}/edit`}
                  size="sm"
                >
                  <IconPencil size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>

          <Divider my={rem(4)} />

          <Group gap={4}>
            <Text size="xs">Referral Method: </Text>
            <Text size="xs">{referralMethod || "N/A"}</Text>
          </Group>
          <Group gap={4}>
            <Text size="xs">Created: </Text>
            <Text size="xs">{formattedCreatedOn}</Text>
          </Group>
          <Group gap={4}>
            <Text size="xs">Updated: </Text>
            <Text size="xs">{formattedUpdatedOn}</Text>
          </Group>
        </Stack>
      </HoverCard.Dropdown>
    </HoverCard>
  );
};

export default PatientHoverCard;
