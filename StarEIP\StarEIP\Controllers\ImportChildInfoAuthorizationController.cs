using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;
using StarEIP.Models.Import;

namespace StarEIP.Controllers
{
    [Route("api/import-child-info-authorization")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.AllowDataImport))]
    public class ImportChildInfoAuthorizationController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;
        private readonly ILogger<ImportChildInfoAuthorizationController> _logger;

        public ImportChildInfoAuthorizationController(StarEipDbContext dbContext, ILogger<ImportChildInfoAuthorizationController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get(string? programId = null)
        {
            try
            {
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                
                _dbContext.ChangeTracker.LazyLoadingEnabled = true;
                _dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                var query = _dbContext.ImportChildInfoAuthorizations.AsQueryable();
                
                // Filter by program ID if provided
                if (!string.IsNullOrEmpty(programId))
                {
                    query = query.Where(a => a.EiChildId == programId);
                }
                
                loadOptions.PrimaryKey = new[] { nameof(ImportChildInfoAuthorization.Id) };
                loadOptions.PaginateViaPrimaryKey = true;

                return Ok(await DataSourceLoader.LoadAsync(query, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching import child info authorizations");
                return StatusCode(500, "An error occurred while fetching the data");
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var authorization = await _dbContext.ImportChildInfoAuthorizations.FindAsync(id);
                
                if (authorization == null)
                {
                    return NotFound();
                }
                
                return Ok(authorization);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching import child info authorization by id");
                return StatusCode(500, "An error occurred while fetching the data");
            }
        }

        [HttpGet("by-program-id/{programId}")]
        public async Task<IActionResult> GetByProgramId(string programId)
        {
            try
            {
                if (string.IsNullOrEmpty(programId))
                {
                    return BadRequest("Program ID cannot be empty");
                }

                var authorizations = await _dbContext.ImportChildInfoAuthorizations
                    .Where(a => a.EiChildId == programId)
                    .ToListAsync();
                
                return Ok(authorizations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching import child info authorizations by program ID");
                return StatusCode(500, "An error occurred while fetching the data");
            }
        }
    }
}
