"use client";

import React, { useState } from "react";
import { AppShell, Box } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import Header from "@/app/(admin)/components/Header";
import Navigation from "@/app/(admin)/components/Navigation";
import { useMainStore } from "@/app/store/MainStore";

const AdminAppShell = ({ children }: { children: React.ReactNode }) => {
  const collapsed = useMainStore((state) => state.sideMenuCollapsed);

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: collapsed ? "60" : 200,
        breakpoint: "sm",
        collapsed: { mobile: collapsed },
      }}
      styles={{
        main: {
          transition: "padding-left 300ms ease",
          height: "100%",
        },
        navbar: {
          transition: "width 300ms ease",
        },
        root: {
          height: "100%",
        },
      }}
    >
      <AppShell.Header>
        <Header />
      </AppShell.Header>
      <AppShell.Navbar>
        <Navigation />
      </AppShell.Navbar>
      <AppShell.Main>
        <Box
          id="AdminAppShell_Main_Body_Box"
          style={{
            width: "100%",
            height: "100%",
            overflow: "auto",
            paddingLeft: "5px",
            paddingTop: "5px",
          }}
        >
          {children}
        </Box>
      </AppShell.Main>
    </AppShell>
  );
};

export default AdminAppShell;
