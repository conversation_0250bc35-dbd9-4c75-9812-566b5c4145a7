import fs from "fs/promises";
import https from "https";
import axios from "axios";
import dotenv from "dotenv";
import orval from "orval";
import path from "path";

dotenv.config({ path: ".env" });
dotenv.config({ path: ".env.staging", override: true });
dotenv.config({ path: ".env.development", override: true });
dotenv.config({ path: ".env.development.local", override: true });

const { NEXT_PUBLIC_HOST_URL } = process.env;

const SWAGGER_URL = `${NEXT_PUBLIC_HOST_URL}/swagger/v1/swagger.json`;
const SWAGGER_FILE_PATH = `./src/api/stareip-swagger.json`;
const GENERATED_API_CLIENT_PATH = "./src/api/generated";
const TRANSFORMERS_PATH = "./src/api/transformer";

const httpsAgent = new https.Agent({
  rejectUnauthorized: false, // Ignore SSL certificate validation
});

const axiosInstance = axios.create({
  httpsAgent,
  family: 4, // Force IPv4
});

async function downloadSwaggerSpecification() {
  console.log("Downloading Swagger specification...", SWAGGER_URL);

  try {
    const response = await axiosInstance.get(SWAGGER_URL);
    await fs.writeFile(
      SWAGGER_FILE_PATH,
      JSON.stringify(response.data, null, 2),
    );
  } catch (error) {
    console.error("Error downloading Swagger specification:", error);
    throw error;
  }
}

async function generateApiClient() {
  console.log("Generating API client...");

  await orval({
    input: {
      target: SWAGGER_FILE_PATH,
      override: {
        transformer: `${TRANSFORMERS_PATH}/update-schema-names.mjs`,
      },
    },
    output: {
      target: `${GENERATED_API_CLIENT_PATH}/`, // Ensures a directory, not a single file
      schemas: `${GENERATED_API_CLIENT_PATH}/schemas`,
      client: "axios-functions",
      clean: true,
      mode: "tags",
    },
  });

  console.log("Generating API wrapper...");
  await generateApiWrapper();
  console.log("✅ API wrapper generated successfully!");
}

async function generateApiWrapper() {
  const files = await fs.readdir(GENERATED_API_CLIENT_PATH);
  const imports = [];
  const apiObject = [];

  for (const file of files) {
    if (file.endsWith(".ts") && file !== "index.ts") {
      let moduleName = path.basename(file, ".ts");
      if (moduleName.includes("-")) {
        moduleName = moduleName.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
      }
      if (moduleName === "public") {
        moduleName = "publicApi";
      }
      imports.push(
        `import * as ${moduleName} from './${path.basename(file, ".ts")}';`,
      );
      apiObject.push(`  ${moduleName},`);
    }
  }

  const content = `
${imports.join("\n")}

export const api = {
${apiObject.join("\n")}
};
`;

  await fs.writeFile(path.join(GENERATED_API_CLIENT_PATH, "index.ts"), content);
}

async function main() {
  const args = process.argv.slice(2);

  if (args.includes("--update")) {
    await downloadSwaggerSpecification();
  }

  await generateApiClient();
  console.log("Done");
}

main();
