"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button, Container, Title } from "@mantine/core";
import { IconArrowLeft } from "@tabler/icons-react";
import ReportPreview from "../ReportPreview";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";

interface ReportDetails {
  id: number;
  name: string;
  displayName: string;
}

interface PageProps {
  params: Promise<{ name: string }>;
}

const ReportDetailsPage: React.FC<PageProps> = ({ params }) => {
  const router = useRouter();
  const [report, setReport] = useState<ReportDetails | null>(null);

  useEffect(() => {
    const fetchReport = async () => {
      try {
        const resolvedParams = await params;
        const response = await axios.get(
          `${urlHelpers.getAbsoluteURL(`api/reports/${resolvedParams.name}`)}`,
        );
        setReport(response.data);
      } catch (error) {
        console.error("Error fetching report:", error);
        // Handle error (e.g., show notification)
      }
    };

    fetchReport();
  }, [params]);

  return (
    <Container
      fluid
      style={{ height: "100%", display: "flex", flexDirection: "column" }}
    >
      <div
        style={{ display: "flex", alignItems: "center", marginBottom: "1rem" }}
      >
        <Button
          leftSection={<IconArrowLeft size={14} />}
          onClick={() => router.push("/admin/reports")}
          variant="subtle"
        >
          Back
        </Button>
        <Title order={2} style={{ margin: "0 auto" }}>
          {report ? report.displayName : "Loading..."}
        </Title>
      </div>
      <div style={{ flex: 1 }}>
        <ReportPreview reportName={report?.name || ""} />
      </div>
    </Container>
  );
};

export default ReportDetailsPage;
