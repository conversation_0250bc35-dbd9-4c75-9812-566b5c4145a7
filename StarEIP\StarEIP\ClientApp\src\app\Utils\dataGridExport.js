import { exportDataGrid } from "devextreme/excel_exporter";
import saveAs from "file-saver";
import { Workbook } from "exceljs";

export default function dataGridExport(e) {
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet("Main sheet");
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function (options) {
      options.excelCell.font = { name: "Arial", size: 12 };
      options.excelCell.alignment = { horizontal: "left" };
    },
  }).then(function () {
    workbook.xlsx.writeBuffer().then(function (buffer) {
      saveAs(
        new Blob([buffer], { type: "application/octet-stream" }),
        "DataGrid.xlsx",
      );
    });
  });
}
