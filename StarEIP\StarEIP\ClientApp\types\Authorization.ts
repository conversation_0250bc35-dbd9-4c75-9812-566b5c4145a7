import { ChildDetailsDtoSchema } from "@/api/types";

export interface Authorization {
  id: number;
  childId: number;
  statusId: string;
  authType?: string;
  programId?: number;
  userId: number | undefined;
  authNumber?: string;
  startDate?: Date;
  endDate?: Date;
  units?: number;
  scStatus?: number;
  scStatusName?: string;
  statusLastUpdated?: Date;
  followUpDate?: Date;
  scStatusLastUpdated?: Date;
  child?: ChildDetailsDtoSchema;
  userName: string;
  userFullName: string;
}

export interface AuthorizationStatus {
  id: string;
  name: string;
}

export interface ScStatusType {
  id: number;
  name: string;
}

export const newAuthorizationId = 0;

export interface AuthorizationWithChild extends Authorization {
  userName: string;
  userFullName: string;
}

export const newAuthorizationWithChildId = 0;
