using StarEIP.Models.Tasks;
using StarEIP.DTOs;

namespace StarEIP.Models.App
{
    public class TaskDetailsDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? DueAt { get; set; }
        public int? AssignedToUserId { get; set; }
        public UserDto? AssignedToUser { get; set; }
        public int? ChildId { get; set; }
        public string? ChildName { get; set; }
    }
}
