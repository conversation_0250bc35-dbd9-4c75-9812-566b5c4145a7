"use client"; // This is a client component

import React, { useCallback, useEffect, useRef, useState } from "react";
import { Box, Drawer, Paper, Stack, Typography } from "@mui/material";
import { usePlacesWidget } from "react-google-autocomplete";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import Form, {
  Item,
  GroupItem,
  Label,
  SimpleItem,
  ButtonItem,
} from "devextreme-react/form";
import "devextreme-react/text-box";
import "devextreme-react/select-box";
import Validator, { RequiredRule } from "devextreme-react/validator";
import "devextreme-react/radio-group";
import { Save, Visibility } from "@mui/icons-material";
import TextBox from "devextreme-react/text-box";
import Autocomplete from "react-google-autocomplete";
import { Template } from "devextreme-react/core/template";
import AddressInput from "./AddressInput";

function AddressAddOrEdit({
  isOpen,
  onClose,
  onSave,
  onRemove,
  initialAddress,
}) {
  const theme = useTheme();
  const formRef = useRef(null);
  const isTabletOrBigger = useMediaQuery(theme.breakpoints.up("sm"));
  const [address, setAddress] = useState({
    id: initialAddress?.id,
    key: initialAddress?.id || initialAddress?.key || Date.now(),
    streetAddress: "",
    apt: "",
    city: "",
    state: "",
    zip: "",
    fromMonth: "",
    fromYear: "",
    toMonth: "",
    toYear: "",
  });

  useEffect(() => {
    if (initialAddress) {
      setAddress({ ...initialAddress });
    } else {
      // Reset address state for adding a new address
      setAddress({
        id: null,
        key: Date.now(), // Ensure a unique key for new addresses
        streetAddress: "",
        apt: "",
        city: "",
        state: "",
        zip: "",
        fromMonth: "",
        fromYear: "",
        toMonth: "",
        toYear: "",
      });
    }
  }, [initialAddress]);

  const handleSave = (e) => {
    e.preventDefault();
    onSave(address); // Call onSave prop with the current address
    onClose();
  };

  const handleRemove = (e) => {
    e.preventDefault();
    if (onRemove && address.id) {
      onRemove(address.id); // Call onRemove prop with the address id
    }
    onClose();
  };

  const saveButtonOptions = {
    icon: "save",
    text: "Save",
    useSubmitBehavior: true,
    stylingMod: "contained",
    type: "default",
    width: 175,
  };
  const removeButtonOptions = {
    icon: "remove",
    text: "Remove",
    useSubmitBehavior: false,
    stylingMod: "contained",
    type: "danger",
    width: 175,
    onClick: () => {
      onSave(household, true);
      onClose();
    },
  };

  const renderAddressInput = () => {
    return (
      <AddressInput
        onAddressSelected={(a) => {
          console.log(
            "onAddressSelected. existing address: ",
            address,
            "new address",
            a,
          );
          setAddress((prevAddress) => {
            const updatedAddress = { ...prevAddress, ...a };
            console.log("updatedAddress", updatedAddress);
            return updatedAddress;
          });
        }}
      />
    );
  };

  return (
    <Drawer
      open={isOpen}
      onClose={onClose}
      anchor="right"
      SlideProps={{
        direction: "left",
        timeout: { enter: 650, exit: 300, appear: 1 },
      }}
      sx={{ width: "100vw" }}
    >
      <form
        onSubmit={handleSave}
        style={{
          width: isTabletOrBigger ? "500px" : "85vw",
          margin: "20px",
          height: "100%",
        }}
      >
        <Form
          stylingMode="outlined"
          ref={formRef}
          labelMode="floating"
          formData={address}
          showValidationSummary={true}
        >
          <GroupItem caption={initialAddress ? "Edit Address" : "Add Address"}>
            <SimpleItem
              name="streetAddress"
              render={renderAddressInput}
              isRequired
            />

            <Item dataField="apt">
              <Label text="Apt # (if any)" />
            </Item>
            <Item dataField="city" isRequired />

            <Item dataField="state">
              <RequiredRule message="State is required" />
            </Item>
            <Item dataField="zip">
              <RequiredRule message="Zip is requited" />
            </Item>
          </GroupItem>
          <GroupItem colCount={2}>
            <ButtonItem buttonOptions={removeButtonOptions} />
            <ButtonItem buttonOptions={saveButtonOptions} />
          </GroupItem>
        </Form>
      </form>
    </Drawer>
  );
}

export default AddressAddOrEdit;
