namespace StarEIP.Models.Auth
{
    // Remember to keep this type in sync with UserPermission type in AuthStore.tsx in the front-end!
    public static class UserPermission
    {
        public static string ViewDashboard => nameof(ViewDashboard);
        public static string ManageUsers => nameof(ManageUsers);
        public static string ViewChildren => nameof(ViewChildren);

        public static string ViewAuthorizations => nameof(ViewAuthorizations);
        public static string AllowManageAuthorization => nameof(AllowManageAuthorization);

        public static string ViewFaxes => nameof(ViewFaxes);
        public static string ViewEmailTemplates => nameof(ViewEmailTemplates);
        public static string ViewPhysicians => nameof(ViewPhysicians);
        public static string ViewAllAuth => nameof(ViewAllAuth);
        public static string ViewReports => nameof(ViewReports);
        public static string ViewTasks => nameof(ViewTasks);

        public static string AllowReportDesigner => nameof(AllowReportDesigner);

        public static string AllowManageContacts => nameof(AllowManageContacts);

        public static string AllowDataImport => nameof(AllowDataImport);
        public static string ViewRejections => nameof(ViewRejections);
        
        public static string ViewAuditLogs => nameof(ViewAuditLogs);

        public static string PermissionClaimName => "Permission";
    }
}

