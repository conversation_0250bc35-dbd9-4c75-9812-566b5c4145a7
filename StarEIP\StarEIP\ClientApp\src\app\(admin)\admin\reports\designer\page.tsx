"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Button, Container, Group, Stack, Title } from "@mantine/core";
import { IconArrowLeft } from "@tabler/icons-react";
import urlHelpers from "@/app/urlHelpers";
import ReportDesigner, {
  RequestOptions,
} from "devexpress-reporting-react/dx-report-designer";

const ReportDesignerPage: React.FC = () => {
  const router = useRouter();
  const iframeUrl = urlHelpers.getAbsoluteURL(`blazor/reportdesigner`);

  return (
    <Stack style={{ height: "100%" }}>
      <Group>
        <Button
          leftSection={<IconArrowLeft size={14} />}
          onClick={() => router.push("/admin/reports")}
          variant="subtle"
        >
          Back
        </Button>
        <Title order={2} style={{ margin: "0 auto" }}>
          Report Designer
        </Title>
      </Group>
      <ReportDesigner reportUrl="" width="100%" height="100%">
        <RequestOptions
          host={urlHelpers.getAbsoluteURL("/")}
          getDesignerModelAction="DXXRD/GetDesignerModel"
        />
      </ReportDesigner>
    </Stack>
  );
};

export default ReportDesignerPage;
