"use client";

import React, { useEffect, useRef, useState } from "react";
import DataGrid, {
  <PERSON>umn,
  Pa<PERSON>,
  Pager,
  FilterRow,
  <PERSON>er<PERSON><PERSON>er,
  ColumnChooser,
  Export,
  Sorting,
  SearchPanel,
  Toolbar,
  Item,
  RemoteOperations,
  DataGridRef,
} from "devextreme-react/data-grid";
import { createStore } from "devextreme-aspnet-data-nojquery";
import urlHelpers from "@/app/urlHelpers";
import { Button } from "devextreme-react/button";
import { IconRefresh } from "@tabler/icons-react";
import { useRouter } from "next/navigation";

interface ImportChildInfoAuthorizationTableProps {
  programId?: string | number | null;
}

const ImportChildInfoAuthorizationTable: React.FC<
  ImportChildInfoAuthorizationTableProps
> = ({ programId }) => {
  const dataGridRef = useRef<DataGridRef>(null);
  const [remoteDataSource, setRemoteDataSource] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    loadDataSource();
  }, [programId]);

  const loadDataSource = () => {
    const baseUrl = urlHelpers.getAbsoluteURL(
      "api/import-child-info-authorization",
    );

    const dataSource = createStore({
      key: "id",
      loadUrl: baseUrl,
      onBeforeSend: (_, ajaxSettings) => {
        // Set JWT token for authentication
        ajaxSettings.headers = {
          Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
        };

        // Add programId filter if provided
        if (programId) {
          ajaxSettings.url = `${ajaxSettings.url}?programId=${programId}`;
        }
      },
      errorHandler(e) {
        if (e.message === "Unauthorized") {
          router.push("/login");
        }
        console.error("Error loading authorization data:", e);
      },
    });

    setRemoteDataSource(dataSource);
  };

  return (
    <div style={{ height: "100%", width: "100%" }}>
      <DataGrid
        ref={dataGridRef}
        dataSource={remoteDataSource}
        remoteOperations
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        showColumnLines={false}
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
      >
        <Toolbar>
          <Item location="before">
            <Button icon="refresh" onClick={loadDataSource} />
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <Paging defaultPageSize={20} />
        <Pager showPageSizeSelector />
        <FilterRow visible />
        <Export enabled={true} />

        <Column
          dataField="authorizationNumber"
          caption="Authorization Number"
        />
        <Column dataField="startDate" caption="Start Date" dataType="date" />
        <Column dataField="endDate" caption="End Date" dataType="date" />
        <Column dataField="id" caption="ID" width={80} visible={false} />
        <Column dataField="eiChildId" caption="EI Child ID" />
        <Column dataField="childFirstName" caption="First Name" />
        <Column dataField="childLastName" caption="Last Name" />
        <Column dataField="dob" caption="Date of Birth" dataType="date" />
        <Column dataField="program" caption="Program" />
        <Column dataField="enrollmentType" caption="Enrollment Type" />
        <Column dataField="enrollmentStatus" caption="Enrollment Status" />
        <Column dataField="locationType" caption="Location Type" />
        <Column dataField="length" caption="Length" />
        <Column dataField="frequency" caption="Frequency" />
        <Column dataField="frequencyUnit" caption="Frequency Unit" />
        <Column
          dataField="totalSessionsAuthorized"
          caption="Total Sessions Authorized"
          dataType="number"
        />
        <Column
          dataField="totalSessionsUsed"
          caption="Total Sessions Used"
          dataType="number"
        />
        <Column
          dataField="remainingUnitsSessions"
          caption="Remaining Units/Sessions"
          dataType="number"
        />
        <Column dataField="therapistFirstName" caption="Therapist First Name" />
        <Column dataField="therapistLastName" caption="Therapist Last Name" />
        <Column dataField="therapistNpi" caption="Therapist NPI" />
        <Column
          dataField="lastSessionDate"
          caption="Last Session Date"
          dataType="date"
        />
      </DataGrid>
    </div>
  );
};

export default ImportChildInfoAuthorizationTable;
