"use client";
import React, { CSSProperties, useEffect } from "react";
import { ColorSchemeScript } from "@mantine/core";
import "@mantine/core/styles.css";
import "@mantine/notifications/styles.css";
import "devextreme/dist/css/dx.light.css";
import "@devexpress/analytics-core/dist/css/dx-analytics.common.css";
import "@devexpress/analytics-core/dist/css/dx-analytics.light.css";
import "ace-builds/css/ace.css";
import "ace-builds/css/theme/dreamweaver.css";
import "ace-builds/css/theme/ambiance.css";
import "@devexpress/analytics-core/dist/css/dx-querybuilder.css";
import "./global.css";
import "./Shared/styles.css";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/next";
import dynamic from "next/dynamic";
import CssLoader from "@/app/Shared/CssLoader";
import Clarity from "@microsoft/clarity";

import "devexpress-reporting/dist/css/dx-webdocumentviewer.css";
import "devexpress-reporting/dist/css/dx-reportdesigner.css";

const ClientLayout = dynamic(() => import("./clientLayout"), {
  loading: () => <CssLoader />,
  ssr: false,
});

// interface RootLayoutProps {
//     children: React.ReactNode;
//     bodyStyle?: CSSProperties;
// }

export default function RootLayout(props: any) {
  const { children, bodyStyle } = props;

  useEffect(() => {
    Clarity.init("pf1n4v37wx");
  }, []);

  return (
    <html lang="en">
      <head>
        <title>Star EIP</title>
        <ColorSchemeScript defaultColorScheme="auto" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        {/* @ts-ignore */}
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100..900;1,100..900&display=swap"
          rel="stylesheet"
        />
      </head>
      <body style={bodyStyle}>
        <SpeedInsights />
        <Analytics />
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
