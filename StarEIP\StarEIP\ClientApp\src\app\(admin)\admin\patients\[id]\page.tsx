"use client";

import React, { useEffect } from "react";
import { useParams } from "next/navigation";
import { Tabs } from "@mantine/core";
import { usePatientStore } from "../PatientStore";
import ChildInfo from "../ChildInfo";
import urlHelpers from "@/app/urlHelpers";
import DocumentStorage from "@/app/(admin)/admin/patients/[id]/components/DocumentStorage";
import AuthorizationsTable from "../../authorizations/AuthorizationsTable";
import axios from "axios";
import { NotesSection } from "../../notes/NotesSection";

const serviceUrl = urlHelpers.getAbsoluteURL("api/children");

const PatientsDetailPage: React.FC = () => {
  const { id } = useParams();

  const { selectedChild, setSelectedChild } = usePatientStore();

  useEffect(() => {
    const fetchChildData = async () => {
      try {
        const response = await axios.get(`${serviceUrl}/${id}`);
        setSelectedChild(response.data);
      } catch (e) {
        console.error("Error fetching child data:", e);
      } finally {
        //setIsLoading(false);
      }
    };
    if (!selectedChild) {
      fetchChildData();
    }
  }, [id]);

  if (!id) {
    return <div>Loading...</div>;
  }

  return (
    <Tabs
      defaultValue="basic_info"
      style={{
        flexGrow: 1,
        display: "flex",
        flexDirection: "column",
        height: "100%",
      }}
    >
      <Tabs.List>
        <Tabs.Tab value="basic_info">Basic Info</Tabs.Tab>
        <Tabs.Tab value="auth">Authorizations</Tabs.Tab>
        <Tabs.Tab value="docs">Document Storage</Tabs.Tab>
        <Tabs.Tab value="notes">Notes</Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="basic_info">
        <ChildInfo child={selectedChild} />
      </Tabs.Panel>

      <Tabs.Panel
        value="auth"
        style={{ height: "100%", display: "flex", flex: 1, flexGrow: 1 }}
      >
        <AuthorizationsTable childId={selectedChild?.id} />
      </Tabs.Panel>

      <Tabs.Panel
        value="docs"
        style={{ height: "100%", display: "flex", flex: 1, flexGrow: 1 }}
      >
        <DocumentStorage childId={Number(id)} />
      </Tabs.Panel>

      <Tabs.Panel value="notes">
        <NotesSection
          entityType="child"
          entityId={Number(id)}
          onNoteAdded={() => {}}
          maxLength={500}
          enableDelete={true}
        />
      </Tabs.Panel>
    </Tabs>
  );
};

export default PatientsDetailPage;
