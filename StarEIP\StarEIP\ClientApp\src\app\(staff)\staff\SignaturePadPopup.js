"use client"; // This is a client component

import * as React from "react";
import {
  AppBar,
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  FormControlLabel,
  IconButton,
  Slide,
  Toolbar,
  Typography,
} from "@mui/material";
import { useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import SignaturePad from "react-signature-pad-wrapper";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const SignaturePadPopup = ({ isOpen, onClose, onSave }) => {
  const theme = useTheme();
  const isTabletOrBigger = useMediaQuery(theme.breakpoints.up("sm"));
  const signaturePadRef = React.useRef();
  const [isAccepted, setIsAccepted] = React.useState(false);
  React.useEffect(() => {
    setIsAccepted(false);
  }, [isOpen]);
  const handleAcceptChange = (event) => {
    setIsAccepted(event.target.checked);
  };

  return (
    <Dialog
      fullScreen={!isTabletOrBigger}
      open={isOpen}
      onClose={onClose}
      TransitionComponent={Transition}
    >
      <AppBar sx={{ position: "relative" }}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={onClose}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
          <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
            Signature Pad
          </Typography>
        </Toolbar>
      </AppBar>

      <DialogContent>
        <Typography variant="subtitle1" sx={{ mb: 1 }}>
          Please sign in the area below
        </Typography>
        <Box
          sx={{
            border: "2px dashed grey",
            borderRadius: "4px",
            backgroundColor: "#f9f9f9",
            padding: 2,
            textAlign: "center",
            height: "200px",
          }}
        >
          <SignaturePad
            ref={signaturePadRef}
            options={{ minWidth: 2, maxWidth: 2, penColor: "rgb(0, 0, 0)" }}
          />
        </Box>
        <FormControlLabel
          control={
            <Checkbox
              checked={isAccepted}
              onChange={handleAcceptChange}
              name="acceptSignature"
              color="primary"
            />
          }
          label="I accept this signature as a valid, legally binding signature."
        />
      </DialogContent>

      <DialogActions>
        <Button onClick={() => signaturePadRef.current.clear()}>Clear</Button>
        <Button
          color="primary"
          variant="contained"
          endIcon={<AddIcon />}
          onClick={() => {
            const signatureData = signaturePadRef.current.toDataURL();
            onSave(signatureData);
          }}
          disabled={!isAccepted} // Disable the button if isAccepted is false
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SignaturePadPopup;
