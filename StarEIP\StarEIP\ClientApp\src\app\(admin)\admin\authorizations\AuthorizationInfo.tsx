import { AuthorizationWithChild } from "../../../../../types/Authorization";
import {
  Card,
  Text,
  Group,
  Title,
  Stack,
  Select,
  Divider,
  Tooltip,
  Button,
  ActionIcon,
  Grid,
} from "@mantine/core";
import React, { ReactElement, useEffect, useState } from "react";
import {
  IconBoxMultiple,
  IconCalendar,
  IconEdit,
  IconId,
  IconUser,
} from "@tabler/icons-react";
import { Popup } from "devextreme-react/popup";
import AuthorizationDetailsPopup from "@/app/(admin)/admin/authorizations/AuthorizationDetailsPopup";
import AuthorizationCardHeader from "@/app/(admin)/admin/authorizations/components/AuthorizationCardHeader";
import axios from "axios";
import urlHelpers from "@/app/urlHelpers";

interface AuthorizationInfoProps {
  authorization: AuthorizationWithChild;
}

interface InfoGroupProps {
  icon: ReactElement<{ size?: number; stroke?: number; color?: string }>;
  label: string;
  value: string | undefined;
}

const InfoGroup: React.FC<InfoGroupProps> = ({ icon, label, value }) => (
  <Tooltip label={label}>
    <Group>
      {React.cloneElement(icon, {
        size: 16,
        stroke: 1.5,
        color: "rgba(0, 0, 0, 0.24)",
      })}
      <Text
        fw={500}
        size="14px"
        style={{
          fontFamily: "Roboto",
          lineHeight: "18px",
          color: "rgba(0, 0, 0, 0.44)",
        }}
      >
        {value}
      </Text>
    </Group>
  </Tooltip>
);

const formatDate = (date: Date | string | undefined): string => {
  if (!date) return "N/A";
  if (typeof date === "string") {
    // If it's an ISO string, parse it first
    return new Date(date).toLocaleDateString();
  }
  if (date instanceof Date) {
    return date.toLocaleDateString();
  }
  return "Invalid Date";
};

const AuthorizationInfo = ({ authorization }: AuthorizationInfoProps) => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [statuses, setStatuses] = useState([]);
  const [currentAuthorization, setCurrentAuthorization] =
    useState(authorization);

  useEffect(() => {
    const fetchStatuses = async () => {
      const response = await axios.get("/api/authorizations/statuses");
      setStatuses(response.data);
    };
    fetchStatuses();
  }, []);
  
  const refreshAuthorization = async () => {
    try {
      const response = await axios.get(
        urlHelpers.getAbsoluteURL(`api/authorizations/${authorization.id}`)
      );
      setCurrentAuthorization(response.data);
    } catch (error) {
      console.error("Error refreshing authorization:", error);
    }
  };

  return (
    <>
      <Grid>
        <Grid.Col span={8}>
          <Card shadow="sm" padding="lg">
            <Stack gap="1">
              <AuthorizationCardHeader
                authorization={currentAuthorization}
                statuses={statuses}
                onEditClick={() => setIsFormVisible(true)}
                showEdit={true}
                onStatusChange={setCurrentAuthorization}
              />

              <Divider my="sm" />

              <Stack gap="8px">
                <InfoGroup
                  icon={<IconId />}
                  label="Type"
                  value={currentAuthorization.authType}
                />
                <InfoGroup
                  icon={<IconCalendar />}
                  label="Start Date"
                  value={formatDate(currentAuthorization.startDate)}
                />
                <InfoGroup
                  icon={<IconCalendar />}
                  label="End Date"
                  value={formatDate(currentAuthorization.endDate)}
                />
                <InfoGroup
                  icon={<IconBoxMultiple />}
                  label="Units"
                  value={currentAuthorization.units?.toString()}
                />
              </Stack>

              <Divider my="sm" label="Assigned To" />
              <InfoGroup
                icon={<IconUser />}
                label="User"
                value={currentAuthorization.userFullName}
              />
            </Stack>
          </Card>
        </Grid.Col>
        
      </Grid>

      {isFormVisible && (
        <Popup
          visible={isFormVisible}
          onHiding={() => setIsFormVisible(false)}
          title="Edit Authorization"
          showCloseButton={true}
          width={800}
          height={"auto"}
        >
          <AuthorizationDetailsPopup
            closeDetails={() => {
              setIsFormVisible(false);
              refreshAuthorization();
            }}
            initialData={currentAuthorization}
            isEditMode={true}
            childId={currentAuthorization.childId}
          />
        </Popup>
      )}
    </>
  );
};

export default AuthorizationInfo;
