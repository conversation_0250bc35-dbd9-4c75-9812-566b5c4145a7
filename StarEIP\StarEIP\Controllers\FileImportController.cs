﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models.Auth;
using StarEIP.Models.Import;
using StarEIP.Services;

namespace StarEIP.Controllers
{
    [Route("api/FileImport")]
    [ApiController]
    [Authorize(Policy = nameof(UserPermission.AllowDataImport))]
    public class FileImportController(StarEipDbContext dbContext) : ControllerBase
    {
        private readonly StarEipDbContext _dbContext = dbContext;

        [HttpPost("PsChildren")]
        public IActionResult ImportChildren([FromBody] List<ChildImportModel> children)
        {
            if (children == null || children.Count == 0)
            {
                return BadRequest("No data provided.");
            }

            // Add your logic to process the imported children data here

            return Ok("Data imported successfully.");
        }

        [HttpGet("ImportTypes")]
        public async Task<ActionResult<List<ImportType>>> GetImportTypes()
        {
            var importTypes = await _dbContext.ImportTypes.ToListAsync();
            return Ok(importTypes);
        }

        [HttpPost("UploadFile/{importKey}")]
        public async Task<IActionResult> UploadFile([FromRoute] string importKey, IFormFile file, [FromServices] ImportService importService)
        {
            if (string.IsNullOrEmpty(importKey))
            {
                return BadRequest("Import key is required.");
            }

            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded.");
            }

            var importType = await _dbContext.ImportTypes.FirstOrDefaultAsync(it => it.ImportKey == importKey);
            if (importType == null)
            {
                return BadRequest("Invalid import type.");
            }

            try
            {
                await using var stream = file.OpenReadStream();
                switch (importKey)
                {
                    case "PS_RECONCILIATION":
                        await importService.ImportReconciliationAsync(stream);
                        return Ok(new { Message = "Reconciliation file imported successfully." });
                    case "HUB_CHILD_INFO_AND_AUTHORIZATION":
                        await importService.ImportChildInfoAuthorizationsAsync(stream);
                        return Ok(new
                        {
                            Message = "Child info and authorization file imported successfully."
                        });
                    case "HUB_CHILD_DETAIL_REPORT":
                        await importService.ImportChildDetailReportAsync(stream);
                        return Ok(new
                        {
                            Message = "Child detail report imported successfully."
                        });
                    case "PS_ALL_CHILDREN":
                        await importService.ImportPsAllChildrenAsync(stream);
                        return Ok(new
                        {
                            Message = "PS All Children file imported successfully."
                        });
                    case "HUB_CHILD_LOOKUP_REPORT":
                        await importService.ImportChildLookupReportAsync(stream);
                        return Ok(new
                        {
                            Message = "Child lookup report imported successfully."
                        });
                    default:
                        return BadRequest("Unsupported import key.");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while processing the file: {ex.Message}");
            }
        }
    }
}