export const IS_PRODUCTION = process.env.NODE_ENV !== "development";

const urlHelpers = {
  getAbsoluteURL(url) {
    //console.log('getAbsoluteURL', url, 'IS_PRODUCTION', IS_PRODUCTION, 'NEXT_PUBLIC_HOST_URL', process.env.NEXT_PUBLIC_HOST_URL);
    //var hostUrl = IS_PRODUCTION ? "" : process.env.NEXT_PUBLIC_HOST_URL || "";
    var hostUrl = process.env.NEXT_PUBLIC_HOST_URL;
    if (url && !url?.startsWith("/")) {
      url = `/${url}`;
    }
    const fullUrl = `${hostUrl}${url || ""}`;
    return fullUrl;
  },
};

export default urlHelpers;
