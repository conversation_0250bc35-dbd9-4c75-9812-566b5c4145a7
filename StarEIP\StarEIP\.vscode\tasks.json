{"version": "2.0.0", "tasks": [{"label": "watch", "type": "shell", "command": "dotnet watch run", "problemMatcher": [{"owner": "csharp", "pattern": [{"regexp": "^\\s*(\\S.*):(\\d+):(\\d+):\\s*(error|warning)\\s*(\\S*):\\s*(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "code": 5, "message": 6}], "background": {"activeOnStart": true, "beginsPattern": "^\\s*watch : Started", "endsPattern": "^\\s*watch : (Finished|Waiting)"}}], "isBackground": true, "presentation": {"reveal": "always", "panel": "shared"}}]}