"use client";

import React, { Suspense, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import axios, { AxiosError } from "axios";
import urlHelpers from "@/app/urlHelpers";
import useAuthStore from "@/app/store/AuthStore";
import CssLoader from "@/app/Shared/CssLoader";
import { inter } from "@/app/fonts";
import { notifications, Notifications } from "@mantine/notifications";
import { MantineProvider } from "@mantine/core";
import config from "devextreme/core/config";
import { IconX } from "@tabler/icons-react";
import "@fontsource/roboto/300.css";
import "@fontsource/roboto/400.css";
import "@fontsource/roboto/500.css";
import "@fontsource/roboto/700.css";
import "@fontsource/roboto/900.css";

config({
  licenseKey:
    "ewogICJmb3JtYXQiOiAxLAogICJjdXN0b21lcklkIjogImRhYmNhMDc2LTVmZGYtMTFlNC04MGMwLTAwMjU5MGQ5ZDVmZiIsCiAgIm1heFZlcnNpb25BbGxvd2VkIjogMjQyCn0=.b8KkLgI0uX71NJIpAFwXX9vgZEuU0Iurg48lqkv/LZKY0SWjbyJD1U/bZsFVejpkntHxXg74wDXzjtodyauJnH5QSSnjuDgr1kR3RWC4TSjF3ovaHvqdWKZVLYLnRbapZwRbCw==",
});

const ClientLayout = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    axios.interceptors.request.use((request) => {
      request.baseURL = urlHelpers.getAbsoluteURL();
      const token = localStorage.getItem("jwtToken");
      if (token) {
        request.headers.Authorization = `Bearer ${token}`;
      }
      return request;
    });

    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          router.push(`/login?returnUrl=${pathname}`);
        } else {
          const errorData = (error as AxiosError)?.response?.data;
          const errorMessage =
            typeof errorData === "string"
              ? errorData
              : (errorData as any)?.message ||
                (errorData as any)?.title ||
                error.message ||
                "An error occurred";
          notifications.show({
            title: "Error",
            message: `Error ${error.response?.status}: ${errorMessage}`,
            icon: <IconX size={16} />,
            color: "red",
            autoClose: 5000,
          });
        }
        return Promise.reject(error);
      },
    );
    setLoaded(true);
  }, []);

  if (!loaded) {
    return <CssLoader />;
  }

  return (
    <MantineProvider
      theme={{
        headings: {
          fontFamily: inter.style.fontFamily,
          sizes: {
            h3: { fontWeight: "500", fontSize: "20px", lineHeight: "16.94px" },
            h4: { fontWeight: "600", fontSize: "24px", lineHeight: "28.13px" },
            h6: { fontWeight: "400", fontSize: "14px", lineHeight: "18px" },
          },
        },

        colors: {
          brand: [
            "#E3F2FD",
            "#BBDEFB",
            "#90CAF9",
            "#64B5F6",
            "#42A5F5",
            "#3498db",
            "#1E88E5",
            "#1976D2",
            "#1565C0",
            "#0D47A1",
          ],
          dark: [
            "#C1C2C5",
            "#A6A7AB",
            "#909296",
            "#5c5f66",
            "#373A40",
            "#2C2E33",
            "#25262b",
            "#1A1B1E",
            "#141517",
            "#101113",
          ],
        },
        primaryColor: "brand",
      }}
    >
      <Notifications position="top-center" zIndex={99999999} />
      <Suspense fallback={<CssLoader />}>{children}</Suspense>
    </MantineProvider>
  );
};

export default ClientLayout;
