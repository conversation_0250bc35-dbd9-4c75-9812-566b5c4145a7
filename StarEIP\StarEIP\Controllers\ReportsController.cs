using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StarEIP.Models;
using StarEIP.Models.Auth;

namespace StarEIP.Controllers
{
    [Route("api/reports")]
    [ApiController]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly StarEipDbContext _dbContext;
        private readonly ILogger<ReportsController> _logger;

        public ReportsController(StarEipDbContext dbContext, ILogger<ReportsController> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var reports = _dbContext.Reports;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                _dbContext.ChangeTracker.LazyLoadingEnabled = true;
                _dbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = new[] { nameof(ReportItem.Id) };
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(reports, loadOptions));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get reports");
                throw;
            }
        }

        [HttpGet("{name}")]
        public async Task<IActionResult> GetByName(string name)
        {
            try
            {
                var report = await _dbContext.Reports.FirstOrDefaultAsync(r => r.Name == name);
                if (report == null)
                {
                    return NotFound();
                }
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in get report by name");
                throw;
            }
        }

        [HttpPut("{id}/updateFields")]
        public async Task<IActionResult> UpdateReportFields(int id, [FromBody] UpdateReportFieldsDto dto)
        {
            try
            {
                var report = await _dbContext.Reports.FindAsync(id);
                if (report == null)
                {
                    return NotFound();
                }

                if (dto.ShowOnChildDetails.HasValue)
                {
                    report.ShowOnChildDetails = dto.ShowOnChildDetails.Value;
                }

                if (dto.ShowOnAuthDetails.HasValue)
                {
                    report.ShowOnAuthDetails = dto.ShowOnAuthDetails.Value;
                }

                // Add more fields here as needed in the future

                await _dbContext.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report fields");
                return StatusCode(500, "An error occurred while updating the report");
            }
        }

        [HttpGet("child-details")]
        public async Task<IActionResult> GetReportsForChildDetails([FromQuery] bool isAuthDetail = false)
        {
            try
            {
                IQueryable<ReportItem> reports;

                if (isAuthDetail)
                {
                    reports = _dbContext.Reports.Where(r => r.ShowOnAuthDetails);
                }
                else
                {
                    reports = _dbContext.Reports.Where(r => r.ShowOnChildDetails);
                }

                var result = await reports
                    .Select(r => new { r.Id, r.Name, r.DisplayName })
                    .ToListAsync();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching reports for {(DetailType)} details", isAuthDetail ? "authorization" : "child");
                return StatusCode(500, "An error occurred while fetching the reports.");
            }
        }
    }

    public class UpdateReportFieldsDto
    {
        public bool? ShowOnChildDetails { get; set; }
        public bool? ShowOnAuthDetails { get; set; }
    }
}
