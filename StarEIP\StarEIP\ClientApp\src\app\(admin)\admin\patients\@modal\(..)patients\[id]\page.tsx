"use client";

import { useParams, usePathname } from "next/navigation";
import Link from "next/link";
import { useEffect } from "react";
import PatientsDetailPage from "../../../[id]/page";
import NewPatientPage from "../../../new/page";

const TaskPage = () => {
  const { id } = useParams();
  const pathname = usePathname();
  console.log("pathname", pathname);
  const isModalOpen = pathname.startsWith("/admin/patients/") && id; // Detect if we're on a modal route

  useEffect(() => {
    const htmlElement = document.querySelector("html");
    if (htmlElement) {
      if (id) {
        htmlElement.style.overflow = "hidden";
      } else {
        htmlElement.style.overflow = "";
      }
    }
    return () => {
      if (htmlElement) {
        htmlElement.style.overflow = "";
      }
    };
  }, [id]);

  if (!isModalOpen) return null; // Don't render the modal if the route isn't modal-related
  console.log("isModalOpen", isModalOpen);

  return (
    <div
      id="patient_intercepted_layout"
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 1,
        backgroundColor: "white",
        opacity: 1,
        overflow: "hidden",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        overflowX: "scroll",
        overflowY: "scroll",
      }}
    >
      {pathname.endsWith("/new") ? <NewPatientPage /> : <PatientsDetailPage />}
    </div>
  );
};

export default TaskPage;
