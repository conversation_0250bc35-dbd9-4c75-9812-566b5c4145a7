using System.ComponentModel.DataAnnotations;

namespace StarEIP.Models
{
    public class ChildReconciliation
    {
        [Key]
        public string RowKey { get; set; }
        public string? ChildId { get; set; }

        // Basic info
        public string? ChildName { get; set; }
        public string? ChildFirstName { get; set; }
        public string? ChildLastName { get; set; }
        public string? Gender { get; set; }
        public string? PrimaryLanguage { get; set; }
        public DateTime? DOB { get; set; }
        public string? GuardianFirstName { get; set; }
        public string? GuardianLastName { get; set; }
        public string? FullAddress { get; set; }

        // Presence indicators
        public bool ExistsInHub { get; set; }
        public bool ExistsInPs { get; set; }
        public bool ExistsInStar { get; set; }

        // Matching indicators
        public bool HubPsMatch { get; set; }
        public bool HubStarMatch { get; set; }
        public bool PsStarMatch { get; set; }

        // Individual system IDs
        public string? HubChildId { get; set; }
        public string? PsChildId { get; set; }
        public int? StarChildId { get; set; }
        public int? StarProgramId { get; set; }

        // Authorization check
        public bool HasActiveAuth { get; set; }

    }
}
