"use client";

import React, { useEffect, useState } from "react";
import { notifications } from "@mantine/notifications";
import { Container, Select, FileInput, Button, Group } from "@mantine/core";
import urlHelpers from "@/app/urlHelpers";
import { api } from "@/api/generated";

const uploadFileUrl = urlHelpers.getAbsoluteURL("api/import/UploadFile");

const ImportPage: React.FC = () => {
  const [importTypes, setImportTypes] = useState<
    { value: string; label: string }[]
  >([]);
  const [selectedImportType, setSelectedImportType] = useState<string | null>(
    null,
  );
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    const fetchImportTypes = async () => {
      try {
        const response = await api.fileImport.getApiFileImportImportTypes();
        const types = response.data.map((d) => ({
          value: d.importKey || "", // Use ImportKey instead of Id, ensure it's a string
          label: d.name ?? "",
        }));
        setImportTypes(types);
      } catch (error: any) {
        console.error("Error fetching import types:", error);
        notifications.show({
          title: "Error",
          message: error?.message || "Failed to fetch import types.",
          color: "red",
        });
      }
    };

    fetchImportTypes();
  }, []);

  const handleUpload = async () => {
    if (!selectedImportType || !file) {
      notifications.show({
        title: "Error",
        message: "Please select an import type and a file.",
        color: "red",
      });
      return;
    }

    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      await api.fileImport.postApiFileImportUploadFileImportKey(
        selectedImportType,
        { file },
      ); // Use ImportKey

      notifications.show({
        title: "Success",
        message: "File uploaded successfully.",
        color: "green",
      });
      setFile(null);
      setSelectedImportType(null);
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Failed to upload file.",
        color: "red",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Container size="sm">
      <h1>Import Files</h1>
      <Select
        label="Select Import Type"
        placeholder="Choose an import type"
        data={importTypes}
        value={selectedImportType}
        onChange={setSelectedImportType}
        required
      />
      <FileInput
        label="Upload File"
        placeholder="Choose a file"
        accept=".csv, .xlsx"
        value={file}
        onChange={setFile}
        required
      />
      <Group justify="flex-end" mt="md">
        <Button onClick={handleUpload} loading={isUploading}>
          Upload
        </Button>
      </Group>
    </Container>
  );
};

export default ImportPage;
